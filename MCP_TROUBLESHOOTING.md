# MCP (Model Context Protocol) 故障排除指南

## 🎯 问题解决状态

### ✅ 已解决的问题
1. **npm权限问题** - 已配置用户级npm全局目录 (`~/.npm-global`)
2. **网络访问问题** - 已配置中国镜像源 (`https://registry.npmmirror.com`)
3. **MCP服务器安装** - 已成功安装3个核心MCP服务器
4. **PATH配置** - 已更新shell环境变量
5. **LunarVim集成** - 已添加mcphub.nvim插件和诊断工具

### 📦 已安装的MCP服务器
- `@modelcontextprotocol/server-filesystem@2025.7.1` - 文件系统操作
- `@modelcontextprotocol/server-github@2025.4.8` - GitHub API集成
- `@modelcontextprotocol/server-sequential-thinking@2025.7.1` - 序列思考

## 🛠️ 使用指南

### 1. 验证安装
在LunarVim中运行以下命令：

```vim
:MCPDiagnostics        " 运行完整诊断
:MCPCheckServers       " 检查服务器状态
:MCPTestServers        " 测试服务器连接
```

### 2. 常用MCP命令
```vim
:MCPInstall           " 安装/重新安装MCP服务器
:MCPStatus            " 显示服务器状态
:MCPFix               " 自动修复配置问题
:MCPCheckDeps         " 检查系统依赖
```

### 3. AI集成快捷键
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `,Am` | MCP Hub | 打开MCP管理界面 |
| `,Ams` | MCP服务器 | 管理MCP服务器 |
| `,Amt` | MCP工具 | 查看可用工具 |
| `,Amr` | MCP资源 | 查看可用资源 |

## 🔧 故障排除

### 问题1: `:MCPInstall` 命令失败
**症状**: 执行`:MCPInstall`时出现npm权限错误或网络错误

**解决方案**:
```bash
# 1. 重新配置npm环境
cd ~/.config/lvim
./install_mcp.sh

# 2. 手动修复权限
mkdir -p ~/.npm-global
npm config set prefix ~/.npm-global
export PATH=~/.npm-global/bin:$PATH

# 3. 重新安装服务器
npm install -g @modelcontextprotocol/server-filesystem
npm install -g @modelcontextprotocol/server-github
npm install -g @modelcontextprotocol/server-sequential-thinking
```

### 问题2: mcphub.nvim插件未加载
**症状**: MCP相关命令不可用，插件未加载

**解决方案**:
```vim
" 1. 检查插件状态
:Lazy

" 2. 强制重新加载插件
:Lazy reload mcphub.nvim

" 3. 检查加载条件
:lua print(vim.fn.executable("npm"), vim.fn.executable("npx"))
```

### 问题3: MCP服务器连接失败
**症状**: 服务器已安装但无法连接

**解决方案**:
```bash
# 1. 检查PATH环境变量
echo $PATH | grep npm-global

# 2. 手动测试服务器
export PATH=~/.npm-global/bin:$PATH
npx @modelcontextprotocol/server-filesystem --help

# 3. 重启LunarVim
```

### 问题4: GitHub服务器需要Token
**症状**: GitHub MCP服务器功能受限

**解决方案**:
```bash
# 1. 创建GitHub Personal Access Token
# 访问: https://github.com/settings/tokens

# 2. 设置环境变量
echo 'export GITHUB_TOKEN=your_token_here' >> ~/.zshrc
source ~/.zshrc

# 3. 在LunarVim中验证
:lua print(os.getenv("GITHUB_TOKEN"))
```

## 🚀 高级配置

### 1. 自定义MCP服务器
编辑 `lua/user/ai/mcp.lua` 添加新服务器：

```lua
M.servers.custom_server = {
  command = "npx",
  args = { "your-custom-mcp-server" },
  env = {},
  description = "自定义MCP服务器",
  capabilities = { "tools" },
  enabled = true,
}
```

### 2. 性能优化
```lua
-- 在 lua/user/ai/mcp.lua 中调整
M.setup({
  -- 减少日志级别
  logging = { level = "warn" },
  
  -- 优化UI性能
  ui = { 
    width = 0.6,  -- 减小窗口大小
    height = 0.6 
  },
})
```

### 3. 容器化部署（可选）
```dockerfile
# Dockerfile.mcp
FROM node:18-alpine
RUN npm install -g @modelcontextprotocol/server-filesystem
EXPOSE 3000
CMD ["npx", "@modelcontextprotocol/server-filesystem"]
```

## 📊 监控和日志

### 1. 查看MCP日志
```bash
# LunarVim缓存目录中的日志
tail -f ~/.cache/lvim/mcphub.log

# npm安装日志
cat ~/.npm/_logs/*debug*.log
```

### 2. 性能监控
```vim
:AIStats              " 查看AI使用统计
:LvimInfo             " 查看LunarVim信息
:checkhealth          " 运行健康检查
```

## 🔄 维护和更新

### 1. 更新MCP服务器
```bash
# 更新所有MCP服务器
npm update -g @modelcontextprotocol/server-filesystem
npm update -g @modelcontextprotocol/server-github
npm update -g @modelcontextprotocol/server-sequential-thinking
```

### 2. 清理和重置
```bash
# 清理npm缓存
npm cache clean --force

# 重置MCP配置
rm -rf ~/.npm-global
./install_mcp.sh
```

### 3. 备份配置
```bash
# 备份重要配置
tar -czf mcp-config-backup.tar.gz \
  ~/.config/lvim/lua/user/ai/ \
  ~/.npm-global/ \
  ~/.npmrc
```

## 📞 获取帮助

### 1. 诊断信息收集
运行以下命令收集诊断信息：
```vim
:MCPDiagnostics
:LvimInfo
:checkhealth
```

### 2. 常见错误代码
- **E404**: npm包不存在 - 检查包名和网络连接
- **EACCES**: 权限错误 - 使用用户级npm目录
- **ENOTFOUND**: 网络错误 - 检查镜像源配置

### 3. 社区资源
- [MCP官方文档](https://modelcontextprotocol.io/)
- [mcphub.nvim GitHub](https://github.com/ravitemer/mcphub.nvim)
- [LunarVim社区](https://lunarvim.org/)

---

**最后更新**: 2025-07-14
**适用环境**: Arch Linux + Wayfire + LunarVim
