# 测试 Avante AI 助手功能
# 这是一个简单的 Python 文件，用于测试 Avante 插件

def fibonacci(n):
    """计算斐波那契数列的第n项"""
    if n <= 1:
        return n
    return fibon<PERSON>ci(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)

def main():
    """主函数"""
    print("测试 Avante AI 助手")
    
    # 计算前10个斐波那契数
    for i in range(10):
        result = fibonacci(i)
        print(f"fibonacci({i}) = {result}")

if __name__ == "__main__":
    main()
