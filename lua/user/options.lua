-- ~/.config/lvim/lua/user/options.lua
-- 核心编辑器选项配置

-- 基础编辑设置
vim.opt.wrap = true -- 自动换行
vim.opt.relativenumber = true -- 显示相对行号
vim.opt.number = true -- 显示当前行号
vim.opt.cursorline = true -- 高亮当前行
vim.opt.cursorcolumn = false -- 不高亮当前列

-- 缩进设置
vim.opt.tabstop = 2 -- Tab 宽度为2个空格
vim.opt.shiftwidth = 2 -- 缩进宽度为2个空格
vim.opt.expandtab = true -- 使用空格替代Tab
vim.opt.smartindent = true -- 智能缩进
vim.opt.autoindent = true -- 自动缩进

-- 中文支持
vim.opt.encoding = "utf-8"
vim.opt.fileencoding = "utf-8"
vim.opt.fileencodings = "utf-8,gbk,gb2312,big5"

-- 搜索设置
vim.opt.ignorecase = true -- 忽略大小写
vim.opt.smartcase = true -- 智能大小写
vim.opt.hlsearch = true -- 高亮搜索结果
vim.opt.incsearch = true -- 增量搜索

-- 折叠设置
vim.opt.foldmethod = "expr"
vim.opt.foldexpr = "nvim_treesitter#foldexpr()"
vim.opt.foldenable = false -- 默认不折叠

-- 分割窗口设置
vim.opt.splitbelow = true -- 水平分割在下方
vim.opt.splitright = true -- 垂直分割在右方

-- 滚动设置
vim.opt.scrolloff = 8 -- 垂直滚动边距
vim.opt.sidescrolloff = 8 -- 水平滚动边距

-- 命令行设置
vim.opt.cmdheight = 1 -- 命令行高度
vim.opt.showmode = false -- 不显示模式

-- 性能优化
vim.opt.updatetime = 250 -- 更新时间
vim.opt.timeoutlen = 300 -- 快捷键超时时间
vim.opt.ttimeoutlen = 10 -- 终端快捷键超时时间
vim.opt.lazyredraw = true -- 延迟重绘
vim.opt.regexpengine = 1 -- 使用旧的正则引擎

-- 文件处理
vim.opt.hidden = true -- 允许隐藏未保存的缓冲区
vim.opt.backup = false -- 不创建备份文件
vim.opt.writebackup = false -- 不创建写入备份
vim.opt.swapfile = false -- 不创建交换文件

-- 大文件处理优化
vim.g.large_file = 1024 * 1024 * 1.5 -- 1.5MB
vim.api.nvim_create_autocmd("BufReadPre", {
  callback = function()
    local file_size = vim.fn.getfsize(vim.fn.expand("%"))
    if file_size > vim.g.large_file then
      vim.opt_local.eventignore:append({
        "FileType",
        "Syntax",
        "BufReadPost",
        "BufReadPre",
      })
      vim.opt_local.undolevels = -1
      vim.opt_local.undoreload = 0
      vim.opt_local.list = false
      vim.notify("大文件模式已启用", vim.log.levels.INFO)
    end
  end,
})
