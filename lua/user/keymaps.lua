-- ~/.config/lvim/lua/user/keymaps.lua
-- 此文件用于存放自定义快捷键

local map = vim.keymap.set
local unmap = vim.keymap.del

-- 示例：为自动换行添加一个开关快捷键 ,tw
map("n", "<leader>tw", function()
  vim.opt.wrap = not vim.opt.wrap:get()
end, { desc = "Toggle Wrap" })

-- CodeCompanion 快捷键
lvim.builtin.which_key.mappings.a = {
  name = "AI Companion",
  c = { "<cmd>CodeCompanionChat<CR>", "Chat" },
  e = { "<cmd>CodeCompanionExplain<CR>", "Explain" },
  r = { "<cmd>CodeCompanionReview<CR>", "Review" },
  t = { "<cmd>CodeCompanionGenerateTests<CR>", "Generate Tests" },
  i = { "<cmd>CodeCompanionInline<CR>", "Inline" },
}

