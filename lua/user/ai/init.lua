-- ~/.config/lvim/lua/user/ai/init.lua
-- AI 集成统一入口

-- 安全加载 AI 相关模块
local function safe_require(module_name)
  local ok, module = pcall(require, "user.ai." .. module_name)
  if not ok then
    vim.notify("⚠️ AI 模块加载失败: " .. module_name, vim.log.levels.DEBUG)
    return nil
  end
  return module
end

-- 加载 AI 模块
local secrets = safe_require("secrets")
local mcp = safe_require("mcp")
local workflows = safe_require("workflows")

-- AI 功能统一快捷键
lvim.builtin.which_key.mappings["A"] = {
  name = "🤖 AI 助手",
  
  -- 基础 AI 功能
  c = { "<cmd>CopilotChatToggle<CR>", "💬 Copilot 聊天" },
  v = { "<cmd>AvanteToggle<CR>", "🎯 Avante 编辑器" },
  o = { "<cmd>CodeCompanionChat<CR>", "🔧 CodeCompanion" },
  a = { "<cmd>Augment chat<CR>", "⚡ Augment 聊天" },
  
  -- MCP 协议
  m = {
    name = "🔗 MCP 协议",
    h = { "<cmd>MCPHub<CR>", "打开 MCP Hub" },
    s = { "<cmd>MCPServers<CR>", "管理服务器" },
    t = { "<cmd>MCPTools<CR>", "查看工具" },
    r = { "<cmd>MCPResources<CR>", "查看资源" },
    p = { "<cmd>MCPPrompts<CR>", "查看提示" },
    c = { "<cmd>MCPConnect<CR>", "连接服务器" },
    d = { "<cmd>MCPDisconnect<CR>", "断开连接" },
  },
  
  -- 代码生成和优化
  g = {
    name = "🎨 代码生成",
    f = { "<cmd>CopilotChatFix<CR>", "修复代码" },
    o = { "<cmd>CopilotChatOptimize<CR>", "优化代码" },
    t = { "<cmd>CopilotChatTests<CR>", "生成测试" },
    d = { "<cmd>CopilotChatDocs<CR>", "生成文档" },
    r = { "<cmd>CopilotChatRefactor<CR>", "重构代码" },
    c = { "<cmd>CopilotChatCommit<CR>", "生成提交信息" },
  },
  
  -- 代码解释和审查
  e = {
    name = "📖 代码解释",
    e = { "<cmd>CopilotChatExplain<CR>", "解释代码" },
    r = { "<cmd>CopilotChatReview<CR>", "代码审查" },
    s = { "<cmd>CopilotChatSummarize<CR>", "代码总结" },
    q = { "<cmd>CopilotChat<CR>", "自由提问" },
  },
  
  -- 中文 AI 功能
  z = {
    name = "🇨🇳 中文助手",
    e = { "<cmd>CopilotChatChineseExplain<CR>", "中文解释" },
    r = { "<cmd>CopilotChatChineseReview<CR>", "中文审查" },
    f = { "<cmd>CopilotChatChineseRefactor<CR>", "中文重构建议" },
    d = { "<cmd>CopilotChatChineseDocs<CR>", "中文文档" },
  },
  
  -- AI 状态和配置
  s = {
    name = "⚙️ AI 状态",
    s = { function()
      if secrets then secrets.show_status() else vim.notify("Secrets 模块未加载", vim.log.levels.WARN) end
    end, "API 密钥状态" },
    m = { function()
      if mcp then mcp.show_server_status() else vim.notify("MCP 模块未加载", vim.log.levels.WARN) end
    end, "MCP 服务器状态" },
    c = { "<cmd>Copilot status<CR>", "Copilot 状态" },
    t = { function()
      if workflows then workflows.run_diagnostics() else vim.notify("Workflows 模块未加载", vim.log.levels.WARN) end
    end, "AI 诊断" },
  },
  
  -- AI 工具安装
  i = {
    name = "📦 AI 工具安装",
    m = { function()
      if mcp then mcp.install_servers() else vim.notify("MCP 模块未加载", vim.log.levels.WARN) end
    end, "安装 MCP 服务器" },
    p = { "<cmd>!pip install --user debugpy black isort flake8 mypy<CR>", "安装 Python AI 工具" },
    n = { "<cmd>!npm install -g @modelcontextprotocol/server-filesystem @modelcontextprotocol/server-git @modelcontextprotocol/server-time<CR>", "安装 Node.js MCP 服务器" },
    r = { "<cmd>!rustup component add rust-analyzer rustfmt clippy<CR>", "安装 Rust AI 工具" },
  },
}

-- AI 自动命令
vim.api.nvim_create_augroup("AIIntegration", { clear = true })

-- 自动检查 API 密钥
vim.api.nvim_create_autocmd("VimEnter", {
  group = "AIIntegration",
  callback = function()
    vim.defer_fn(function()
      local secrets = require("user.ai.secrets")
      local status = secrets.check_all_keys()
      local missing = {}
      
      for service, available in pairs(status) do
        if not available then
          table.insert(missing, service)
        end
      end
      
      if #missing > 0 then
        vim.notify(
          "⚠️ 缺少 API 密钥: " .. table.concat(missing, ", ") .. 
          "\n请在环境变量中设置相应的 API 密钥",
          vim.log.levels.WARN
        )
      end
    end, 2000) -- 延迟 2 秒检查
  end,
})

-- 文件类型特定的 AI 功能
vim.api.nvim_create_autocmd("FileType", {
  group = "AIIntegration",
  pattern = { "go", "rust", "python", "c", "cpp", "javascript", "typescript", "lua" },
  callback = function()
    -- 为编程语言文件启用更多 AI 功能
    vim.opt_local.spell = false -- 关闭拼写检查
    
    -- 设置语言特定的 AI 提示
    local filetype = vim.bo.filetype
    local prompts = {
      go = "你是一个 Go 语言专家，请用中文回答问题。",
      rust = "你是一个 Rust 语言专家，请用中文回答问题。",
      python = "你是一个 Python 语言专家，请用中文回答问题。",
      c = "你是一个 C 语言专家，请用中文回答问题。",
      cpp = "你是一个 C++ 语言专家，请用中文回答问题。",
      javascript = "你是一个 JavaScript 专家，请用中文回答问题。",
      typescript = "你是一个 TypeScript 专家，请用中文回答问题。",
      lua = "你是一个 Lua 语言专家，请用中文回答问题。",
    }
    
    if prompts[filetype] then
      vim.b.ai_context = prompts[filetype]
    end
  end,
})

-- AI 性能监控
local ai_stats = {
  copilot_suggestions = 0,
  chat_sessions = 0,
  code_generations = 0,
}

-- 统计 AI 使用情况
vim.api.nvim_create_autocmd("User", {
  group = "AIIntegration",
  pattern = "CopilotSuggestion",
  callback = function()
    ai_stats.copilot_suggestions = ai_stats.copilot_suggestions + 1
  end,
})

-- 显示 AI 使用统计
local function show_ai_stats()
  local lines = {
    "🤖 AI 使用统计:",
    "  💡 Copilot 建议: " .. ai_stats.copilot_suggestions,
    "  💬 聊天会话: " .. ai_stats.chat_sessions,
    "  🎨 代码生成: " .. ai_stats.code_generations,
  }
  vim.notify(table.concat(lines, "\n"), vim.log.levels.INFO)
end

-- 添加统计命令
vim.api.nvim_create_user_command("AIStats", show_ai_stats, {
  desc = "显示 AI 使用统计",
})

-- AI 快速切换功能
local function toggle_ai_features()
  local current_state = vim.g.ai_enabled or true
  vim.g.ai_enabled = not current_state
  
  if vim.g.ai_enabled then
    vim.cmd("Copilot enable")
    vim.notify("✅ AI 功能已启用", vim.log.levels.INFO)
  else
    vim.cmd("Copilot disable")
    vim.notify("❌ AI 功能已禁用", vim.log.levels.INFO)
  end
end

-- 添加 AI 切换命令
vim.api.nvim_create_user_command("AIToggle", toggle_ai_features, {
  desc = "切换 AI 功能开关",
})

-- 智能代码补全增强
vim.api.nvim_create_autocmd("InsertEnter", {
  group = "AIIntegration",
  callback = function()
    -- 在插入模式时启用更积极的 AI 建议
    if vim.g.ai_enabled ~= false then
      vim.defer_fn(function()
        if vim.fn.mode() == "i" then
          -- 触发 Copilot 建议
          vim.cmd("silent! Copilot suggest")
        end
      end, 500)
    end
  end,
})

-- 导出模块
local M = {}

M.stats = ai_stats
M.toggle = toggle_ai_features
M.show_stats = show_ai_stats

return M
