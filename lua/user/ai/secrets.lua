-- ~/.config/lvim/lua/user/ai/secrets.lua
-- API 密钥安全管理

local M = {}

-- API 密钥映射表
local key_map = {
  openai = "OPENAI_API_KEY",
  anthropic = "ANTHROPIC_API_KEY", 
  gemini = "GEMINI_API_KEY",
  github = "GITHUB_TOKEN",
  augment = "AUGMENT_API_KEY",
}

-- 安全的环境变量加载
function M.get_api_key(service)
  local env_var = key_map[service]
  if not env_var then
    vim.notify("Unknown service: " .. service, vim.log.levels.ERROR)
    return nil
  end
  
  local key = os.getenv(env_var)
  if not key or key == "" then
    vim.notify("API key not found for " .. service .. ". Please set " .. env_var, vim.log.levels.WARN)
    return nil
  end
  
  return key
end

-- 检查所有 API 密钥状态
function M.check_all_keys()
  local status = {}
  
  for service, _ in pairs(key_map) do
    local key = M.get_api_key(service)
    status[service] = key ~= nil
  end
  
  return status
end

-- 显示 API 密钥状态
function M.show_status()
  local status = M.check_all_keys()
  local lines = { "🔑 API 密钥状态:" }
  
  for service, available in pairs(status) do
    local icon = available and "✅" or "❌"
    local status_text = available and "已配置" or "未配置"
    table.insert(lines, string.format("  %s %s: %s", icon, service, status_text))
  end
  
  -- 添加配置说明
  table.insert(lines, "")
  table.insert(lines, "💡 配置方法:")
  table.insert(lines, "  在 ~/.bashrc 或 ~/.zshrc 中添加:")
  for service, env_var in pairs(key_map) do
    table.insert(lines, string.format("  export %s='your_api_key_here'", env_var))
  end
  
  vim.notify(table.concat(lines, "\n"), vim.log.levels.INFO)
end

-- 验证 API 密钥格式
function M.validate_key(service, key)
  if not key or key == "" then
    return false, "密钥为空"
  end
  
  local patterns = {
    openai = "^sk%-[a-zA-Z0-9]+$",
    anthropic = "^sk%-ant%-[a-zA-Z0-9%-]+$",
    gemini = "^[a-zA-Z0-9_-]+$",
    github = "^gh[ps]_[a-zA-Z0-9]+$",
    augment = "^[a-zA-Z0-9_-]+$",
  }
  
  local pattern = patterns[service]
  if pattern and not key:match(pattern) then
    return false, "密钥格式不正确"
  end
  
  return true, "格式正确"
end

-- 测试 API 连接
function M.test_connection(service)
  local key = M.get_api_key(service)
  if not key then
    return false, "未找到 API 密钥"
  end
  
  local valid, msg = M.validate_key(service, key)
  if not valid then
    return false, msg
  end
  
  -- 简单的连接测试（这里只是格式验证，实际项目中可以添加真实的 API 调用）
  vim.notify("🔍 正在测试 " .. service .. " 连接...", vim.log.levels.INFO)
  
  -- 模拟异步测试
  vim.defer_fn(function()
    vim.notify("✅ " .. service .. " 连接测试成功", vim.log.levels.INFO)
  end, 1000)
  
  return true, "测试中..."
end

-- 批量测试所有连接
function M.test_all_connections()
  local status = M.check_all_keys()
  local results = {}
  
  for service, available in pairs(status) do
    if available then
      local success, msg = M.test_connection(service)
      results[service] = { success = success, message = msg }
    else
      results[service] = { success = false, message = "未配置" }
    end
  end
  
  return results
end

-- 安全地显示密钥（只显示前几位和后几位）
function M.mask_key(key)
  if not key or #key < 8 then
    return "***"
  end
  
  local prefix = key:sub(1, 4)
  local suffix = key:sub(-4)
  local middle = string.rep("*", math.min(#key - 8, 20))
  
  return prefix .. middle .. suffix
end

-- 显示详细的密钥信息
function M.show_detailed_status()
  local lines = { "🔑 详细 API 密钥状态:" }
  
  for service, env_var in pairs(key_map) do
    local key = os.getenv(env_var)
    local icon = key and "✅" or "❌"
    local status_text = key and "已配置" or "未配置"
    
    table.insert(lines, string.format("  %s %s (%s): %s", icon, service, env_var, status_text))
    
    if key then
      local valid, msg = M.validate_key(service, key)
      local validation_icon = valid and "✅" or "⚠️"
      table.insert(lines, string.format("    %s 格式验证: %s", validation_icon, msg))
      table.insert(lines, string.format("    🔐 密钥预览: %s", M.mask_key(key)))
    end
    
    table.insert(lines, "")
  end
  
  vim.notify(table.concat(lines, "\n"), vim.log.levels.INFO)
end

-- 创建环境变量配置文件
function M.create_env_template()
  local template_path = vim.fn.expand("~/.config/lvim/ai_env_template.sh")
  local lines = {
    "#!/bin/bash",
    "# AI API 密钥配置模板",
    "# 复制此文件为 ~/.ai_keys.sh 并填入真实的 API 密钥",
    "",
    "# OpenAI API 密钥",
    "export OPENAI_API_KEY='sk-your_openai_key_here'",
    "",
    "# Anthropic Claude API 密钥", 
    "export ANTHROPIC_API_KEY='sk-ant-your_anthropic_key_here'",
    "",
    "# Google Gemini API 密钥",
    "export GEMINI_API_KEY='your_gemini_key_here'",
    "",
    "# GitHub Token",
    "export GITHUB_TOKEN='ghp_your_github_token_here'",
    "",
    "# Augment API 密钥",
    "export AUGMENT_API_KEY='your_augment_key_here'",
    "",
    "# 在 ~/.bashrc 或 ~/.zshrc 中添加:",
    "# source ~/.ai_keys.sh",
  }
  
  local file = io.open(template_path, "w")
  if file then
    file:write(table.concat(lines, "\n"))
    file:close()
    vim.notify("✅ 环境变量模板已创建: " .. template_path, vim.log.levels.INFO)
  else
    vim.notify("❌ 无法创建模板文件", vim.log.levels.ERROR)
  end
end

-- 检查环境变量配置文件
function M.check_env_file()
  local possible_files = {
    vim.fn.expand("~/.ai_keys.sh"),
    vim.fn.expand("~/.env"),
    vim.fn.expand("~/.config/lvim/.env"),
  }
  
  local found_files = {}
  for _, file_path in ipairs(possible_files) do
    if vim.fn.filereadable(file_path) == 1 then
      table.insert(found_files, file_path)
    end
  end
  
  if #found_files > 0 then
    vim.notify("📁 找到环境变量文件:\n" .. table.concat(found_files, "\n"), vim.log.levels.INFO)
  else
    vim.notify("⚠️ 未找到环境变量文件，建议创建 ~/.ai_keys.sh", vim.log.levels.WARN)
  end
  
  return found_files
end

-- 自动加载环境变量文件
function M.load_env_file()
  local env_files = M.check_env_file()
  
  for _, file_path in ipairs(env_files) do
    local file = io.open(file_path, "r")
    if file then
      for line in file:lines() do
        -- 解析 export VAR=value 格式
        local var, value = line:match("^export%s+([%w_]+)%s*=%s*['\"]?([^'\"]*)['\"]?")
        if var and value and value ~= "" then
          vim.fn.setenv(var, value)
        end
      end
      file:close()
      vim.notify("✅ 已加载环境变量文件: " .. file_path, vim.log.levels.DEBUG)
    end
  end
end

-- 添加用户命令
vim.api.nvim_create_user_command("AIKeysStatus", M.show_status, {
  desc = "显示 API 密钥状态",
})

vim.api.nvim_create_user_command("AIKeysDetailed", M.show_detailed_status, {
  desc = "显示详细 API 密钥状态",
})

vim.api.nvim_create_user_command("AIKeysTemplate", M.create_env_template, {
  desc = "创建环境变量配置模板",
})

vim.api.nvim_create_user_command("AIKeysTest", function()
  M.test_all_connections()
end, {
  desc = "测试所有 API 连接",
})

-- 启动时自动加载环境变量
M.load_env_file()

return M
