-- ~/.config/lvim/lua/user/ai/mcp_diagnostics.lua
-- MCP 诊断和故障排除工具

local M = {}

-- 颜色输出函数
local function log_success(msg)
  vim.notify("✅ " .. msg, vim.log.levels.INFO)
end

local function log_warning(msg)
  vim.notify("⚠️ " .. msg, vim.log.levels.WARN)
end

local function log_error(msg)
  vim.notify("❌ " .. msg, vim.log.levels.ERROR)
end

local function log_info(msg)
  vim.notify("ℹ️ " .. msg, vim.log.levels.INFO)
end

-- 检查系统依赖
function M.check_system_dependencies()
  log_info("检查系统依赖...")
  
  local deps = {
    { cmd = "node", name = "Node.js" },
    { cmd = "npm", name = "npm" },
    { cmd = "npx", name = "npx" },
  }
  
  local all_ok = true
  for _, dep in ipairs(deps) do
    if vim.fn.executable(dep.cmd) == 1 then
      local version = vim.fn.system(dep.cmd .. " --version"):gsub("\n", "")
      log_success(dep.name .. " 已安装: " .. version)
    else
      log_error(dep.name .. " 未安装")
      all_ok = false
    end
  end
  
  return all_ok
end

-- 检查npm配置
function M.check_npm_config()
  log_info("检查 npm 配置...")
  
  local prefix = vim.fn.system("npm config get prefix"):gsub("\n", "")
  local registry = vim.fn.system("npm config get registry"):gsub("\n", "")
  
  log_info("npm prefix: " .. prefix)
  log_info("npm registry: " .. registry)
  
  -- 检查全局安装目录权限
  local npm_global = vim.fn.expand("~/.npm-global")
  if vim.fn.isdirectory(npm_global) == 1 then
    log_success("用户级 npm 全局目录已配置: " .. npm_global)
  else
    log_warning("用户级 npm 全局目录未配置")
  end
  
  -- 检查PATH配置
  local path = os.getenv("PATH") or ""
  if path:match(npm_global .. "/bin") then
    log_success("npm 全局 bin 目录在 PATH 中")
  else
    log_warning("npm 全局 bin 目录不在 PATH 中")
  end
end

-- 检查MCP服务器安装状态
function M.check_mcp_servers()
  log_info("检查 MCP 服务器安装状态...")
  
  local servers = {
    "@modelcontextprotocol/server-filesystem",
    "@modelcontextprotocol/server-github",
    "@modelcontextprotocol/server-sequential-thinking",
  }
  
  for _, server in ipairs(servers) do
    local cmd = "npm list -g " .. server .. " 2>/dev/null"
    local result = vim.fn.system(cmd)
    
    if vim.v.shell_error == 0 then
      local version = result:match(server .. "@([%d%.%-]+)")
      log_success(server .. " 已安装" .. (version and (" v" .. version) or ""))
    else
      log_error(server .. " 未安装")
    end
  end
end

-- 测试MCP服务器连接
function M.test_mcp_servers()
  log_info("测试 MCP 服务器连接...")
  
  local servers = {
    {
      name = "filesystem",
      cmd = "npx @modelcontextprotocol/server-filesystem " .. vim.fn.getcwd(),
    },
    {
      name = "github", 
      cmd = "npx @modelcontextprotocol/server-github",
    },
    {
      name = "sequential-thinking",
      cmd = "npx @modelcontextprotocol/server-sequential-thinking",
    },
  }
  
  for _, server in ipairs(servers) do
    log_info("测试 " .. server.name .. " 服务器...")
    
    -- 简单的可执行性测试
    local test_cmd = server.cmd .. " --help 2>/dev/null || echo 'help_not_available'"
    local result = vim.fn.system(test_cmd)
    
    if result and not result:match("command not found") then
      log_success(server.name .. " 服务器可执行")
    else
      log_error(server.name .. " 服务器无法执行")
    end
  end
end

-- 检查mcphub.nvim插件状态
function M.check_mcphub_plugin()
  log_info("检查 mcphub.nvim 插件状态...")
  
  local ok, mcphub = pcall(require, "mcphub")
  if ok then
    log_success("mcphub.nvim 插件已加载")
    
    -- 检查插件版本
    if mcphub.version then
      log_info("插件版本: " .. mcphub.version)
    end
  else
    log_error("mcphub.nvim 插件未加载")
  end
  
  -- 检查插件文件
  local plugin_path = vim.fn.stdpath("data") .. "/site/pack/lazy/opt/mcphub.nvim"
  if vim.fn.isdirectory(plugin_path) == 1 then
    log_success("mcphub.nvim 插件文件存在: " .. plugin_path)
  else
    log_error("mcphub.nvim 插件文件不存在")
  end
end

-- 运行完整诊断
function M.run_full_diagnostics()
  log_info("开始 MCP 完整诊断...")
  print("=" .. string.rep("=", 50))
  
  M.check_system_dependencies()
  print("")
  
  M.check_npm_config()
  print("")
  
  M.check_mcp_servers()
  print("")
  
  M.test_mcp_servers()
  print("")
  
  M.check_mcphub_plugin()
  print("")
  
  log_info("MCP 诊断完成！")
  print("=" .. string.rep("=", 50))
end

-- 自动修复常见问题
function M.auto_fix()
  log_info("开始自动修复 MCP 配置问题...")
  
  -- 1. 配置用户级npm目录
  local npm_global = vim.fn.expand("~/.npm-global")
  if vim.fn.isdirectory(npm_global) == 0 then
    vim.fn.system("mkdir -p " .. npm_global)
    vim.fn.system("npm config set prefix " .. npm_global)
    log_success("已配置用户级 npm 全局目录")
  end
  
  -- 2. 配置中国镜像源
  local current_registry = vim.fn.system("npm config get registry"):gsub("\n", "")
  if not current_registry:match("npmmirror.com") then
    vim.fn.system("npm config set registry https://registry.npmmirror.com")
    log_success("已配置 npm 中国镜像源")
  end
  
  -- 3. 更新PATH环境变量
  local path = os.getenv("PATH") or ""
  local npm_bin = npm_global .. "/bin"
  if not path:match(npm_bin) then
    vim.fn.setenv("PATH", npm_bin .. ":" .. path)
    log_success("已更新 PATH 环境变量")
  end
  
  log_info("自动修复完成！建议重启终端以应用更改。")
end

-- 创建用户命令
vim.api.nvim_create_user_command("MCPDiagnostics", M.run_full_diagnostics, {
  desc = "运行 MCP 完整诊断",
})

vim.api.nvim_create_user_command("MCPFix", M.auto_fix, {
  desc = "自动修复 MCP 配置问题",
})

vim.api.nvim_create_user_command("MCPCheckDeps", M.check_system_dependencies, {
  desc = "检查 MCP 系统依赖",
})

vim.api.nvim_create_user_command("MCPCheckServers", M.check_mcp_servers, {
  desc = "检查 MCP 服务器安装状态",
})

vim.api.nvim_create_user_command("MCPTestServers", M.test_mcp_servers, {
  desc = "测试 MCP 服务器连接",
})

return M
