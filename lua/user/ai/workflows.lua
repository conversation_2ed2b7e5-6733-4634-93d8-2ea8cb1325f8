-- ~/.config/lvim/lua/user/ai/workflows.lua
-- AI 工作流和自动化配置

local M = {}

-- AI 工作流统计
M.stats = {
  code_generations = 0,
  explanations = 0,
  reviews = 0,
  optimizations = 0,
  tests_generated = 0,
  docs_generated = 0,
}

-- 自定义 AI 提示模板
M.prompts = {
  chinese_explain = {
    title = "中文代码解释",
    prompt = "请用中文详细解释以下代码的功能、工作原理和关键概念：\n\n```{filetype}\n{code}\n```\n\n请包括：\n1. 代码的主要功能\n2. 关键算法或逻辑\n3. 可能的改进建议",
    mapping = "<leader>Ace",
  },
  
  performance_analysis = {
    title = "性能分析",
    prompt = "请分析以下代码的性能特征并提供优化建议：\n\n```{filetype}\n{code}\n```\n\n请分析：\n1. 时间复杂度\n2. 空间复杂度\n3. 潜在的性能瓶颈\n4. 具体的优化方案",
    mapping = "<leader>Apa",
  },
  
  security_review = {
    title = "安全审查",
    prompt = "请对以下代码进行安全审查，识别潜在的安全风险：\n\n```{filetype}\n{code}\n```\n\n请检查：\n1. 输入验证\n2. 权限控制\n3. 数据泄露风险\n4. 注入攻击防护",
    mapping = "<leader>Asr",
  },
  
  test_generation = {
    title = "测试用例生成",
    prompt = "请为以下代码生成全面的测试用例：\n\n```{filetype}\n{code}\n```\n\n请包括：\n1. 正常情况测试\n2. 边界条件测试\n3. 异常情况测试\n4. 性能测试（如适用）",
    mapping = "<leader>Atg",
  },
  
  documentation = {
    title = "文档生成",
    prompt = "请为以下代码生成详细的中文文档：\n\n```{filetype}\n{code}\n```\n\n请包括：\n1. 函数/类的用途说明\n2. 参数说明\n3. 返回值说明\n4. 使用示例\n5. 注意事项",
    mapping = "<leader>Adg",
  },
}

-- 获取选中的代码
local function get_selected_code()
  local start_pos = vim.fn.getpos("'<")
  local end_pos = vim.fn.getpos("'>")
  local lines = vim.fn.getline(start_pos[2], end_pos[2])
  
  if #lines == 0 then
    return ""
  end
  
  -- 处理部分选择
  if #lines == 1 then
    lines[1] = string.sub(lines[1], start_pos[3], end_pos[3])
  else
    lines[1] = string.sub(lines[1], start_pos[3])
    lines[#lines] = string.sub(lines[#lines], 1, end_pos[3])
  end
  
  return table.concat(lines, "\n")
end

-- 获取当前文件类型
local function get_filetype()
  return vim.bo.filetype or "text"
end

-- 执行 AI 提示
function M.execute_prompt(prompt_key)
  local prompt_config = M.prompts[prompt_key]
  if not prompt_config then
    vim.notify("未找到提示: " .. prompt_key, vim.log.levels.ERROR)
    return
  end
  
  local code = get_selected_code()
  if code == "" then
    vim.notify("请先选择要分析的代码", vim.log.levels.WARN)
    return
  end
  
  local filetype = get_filetype()
  local formatted_prompt = prompt_config.prompt:gsub("{code}", code):gsub("{filetype}", filetype)
  
  -- 更新统计
  if prompt_key:match("explain") then
    M.stats.explanations = M.stats.explanations + 1
  elseif prompt_key:match("review") or prompt_key:match("security") then
    M.stats.reviews = M.stats.reviews + 1
  elseif prompt_key:match("performance") or prompt_key:match("optimization") then
    M.stats.optimizations = M.stats.optimizations + 1
  elseif prompt_key:match("test") then
    M.stats.tests_generated = M.stats.tests_generated + 1
  elseif prompt_key:match("doc") then
    M.stats.docs_generated = M.stats.docs_generated + 1
  end
  
  -- 尝试使用可用的 AI 插件
  local success = false
  
  -- 尝试 CopilotChat
  if not success then
    local ok, _ = pcall(vim.cmd, "CopilotChat " .. formatted_prompt)
    if ok then
      success = true
      vim.notify("✅ 已发送到 CopilotChat: " .. prompt_config.title, vim.log.levels.INFO)
    end
  end
  
  -- 尝试 CodeCompanion
  if not success then
    local ok, _ = pcall(vim.cmd, "CodeCompanionChat " .. formatted_prompt)
    if ok then
      success = true
      vim.notify("✅ 已发送到 CodeCompanion: " .. prompt_config.title, vim.log.levels.INFO)
    end
  end
  
  -- 尝试 Avante
  if not success then
    local ok, _ = pcall(vim.cmd, "AvanteAsk " .. formatted_prompt)
    if ok then
      success = true
      vim.notify("✅ 已发送到 Avante: " .. prompt_config.title, vim.log.levels.INFO)
    end
  end
  
  if not success then
    vim.notify("❌ 没有可用的 AI 插件", vim.log.levels.ERROR)
    -- 作为备选，复制到剪贴板
    vim.fn.setreg("+", formatted_prompt)
    vim.notify("💡 提示已复制到剪贴板", vim.log.levels.INFO)
  end
end

-- 批量代码分析
function M.batch_analysis()
  local code = get_selected_code()
  if code == "" then
    vim.notify("请先选择要分析的代码", vim.log.levels.WARN)
    return
  end
  
  vim.notify("🔍 开始批量代码分析...", vim.log.levels.INFO)
  
  -- 依次执行多个分析
  local analyses = { "chinese_explain", "performance_analysis", "security_review" }
  
  for i, analysis in ipairs(analyses) do
    vim.defer_fn(function()
      M.execute_prompt(analysis)
    end, i * 2000) -- 每2秒执行一个分析
  end
end

-- 智能代码重构建议
function M.smart_refactor()
  local code = get_selected_code()
  if code == "" then
    vim.notify("请先选择要重构的代码", vim.log.levels.WARN)
    return
  end
  
  local filetype = get_filetype()
  local refactor_prompt = string.format([[
请为以下 %s 代码提供重构建议：

```%s
%s
```

请提供：
1. 代码质量评估
2. 具体的重构建议
3. 重构后的代码示例
4. 重构的好处说明

请用中文回答。
]], filetype, filetype, code)
  
  -- 发送到 AI 助手
  local ok, _ = pcall(vim.cmd, "CopilotChat " .. refactor_prompt)
  if ok then
    M.stats.optimizations = M.stats.optimizations + 1
    vim.notify("✅ 重构建议已发送到 CopilotChat", vim.log.levels.INFO)
  else
    vim.fn.setreg("+", refactor_prompt)
    vim.notify("💡 重构提示已复制到剪贴板", vim.log.levels.INFO)
  end
end

-- 生成项目文档
function M.generate_project_docs()
  local project_root = vim.fn.getcwd()
  local project_name = vim.fn.fnamemodify(project_root, ":t")
  
  local doc_prompt = string.format([[
请为项目 "%s" 生成详细的中文文档。

项目路径: %s

请包括：
1. 项目概述
2. 安装说明
3. 使用方法
4. API 文档（如适用）
5. 贡献指南
6. 许可证信息

请分析项目结构并生成相应的 README.md 内容。
]], project_name, project_root)
  
  local ok, _ = pcall(vim.cmd, "CopilotChat " .. doc_prompt)
  if ok then
    M.stats.docs_generated = M.stats.docs_generated + 1
    vim.notify("✅ 项目文档生成请求已发送", vim.log.levels.INFO)
  else
    vim.fn.setreg("+", doc_prompt)
    vim.notify("💡 文档生成提示已复制到剪贴板", vim.log.levels.INFO)
  end
end

-- AI 诊断和健康检查
function M.run_diagnostics()
  local diagnostics = {
    "🔍 AI 工作流诊断报告",
    "",
    "📊 使用统计:",
    "  代码生成: " .. M.stats.code_generations,
    "  代码解释: " .. M.stats.explanations,
    "  代码审查: " .. M.stats.reviews,
    "  性能优化: " .. M.stats.optimizations,
    "  测试生成: " .. M.stats.tests_generated,
    "  文档生成: " .. M.stats.docs_generated,
    "",
    "🔌 AI 插件状态:",
  }
  
  -- 检查 AI 插件状态
  local plugins = {
    { name = "CopilotChat", cmd = "CopilotStatus" },
    { name = "CodeCompanion", cmd = "CodeCompanionStatus" },
    { name = "Avante", cmd = "AvanteStatus" },
  }
  
  for _, plugin in ipairs(plugins) do
    local ok, _ = pcall(vim.cmd, plugin.cmd)
    local status = ok and "✅ 可用" or "❌ 不可用"
    table.insert(diagnostics, "  " .. plugin.name .. ": " .. status)
  end
  
  table.insert(diagnostics, "")
  table.insert(diagnostics, "🔑 API 密钥状态:")
  
  -- 检查 API 密钥
  local secrets = require("user.ai.secrets")
  local key_status = secrets.check_all_keys()
  
  for service, available in pairs(key_status) do
    local status = available and "✅ 已配置" or "❌ 未配置"
    table.insert(diagnostics, "  " .. service .. ": " .. status)
  end
  
  vim.notify(table.concat(diagnostics, "\n"), vim.log.levels.INFO)
end

-- 重置统计
function M.reset_stats()
  M.stats = {
    code_generations = 0,
    explanations = 0,
    reviews = 0,
    optimizations = 0,
    tests_generated = 0,
    docs_generated = 0,
  }
  vim.notify("📊 AI 工作流统计已重置", vim.log.levels.INFO)
end

-- 导出统计报告
function M.export_stats()
  local report_file = vim.fn.stdpath("cache") .. "/ai_workflow_stats.json"
  local stats_with_timestamp = vim.tbl_extend("force", M.stats, {
    timestamp = os.date("%Y-%m-%d %H:%M:%S"),
    session_id = vim.fn.localtime(),
  })
  
  local json_content = vim.fn.json_encode(stats_with_timestamp)
  local file = io.open(report_file, "w")
  
  if file then
    file:write(json_content)
    file:close()
    vim.notify("📊 统计报告已导出: " .. report_file, vim.log.levels.INFO)
  else
    vim.notify("❌ 无法导出统计报告", vim.log.levels.ERROR)
  end
end

-- 设置快捷键
local function setup_keymaps()
  -- 为每个提示设置快捷键
  for key, prompt in pairs(M.prompts) do
    if prompt.mapping then
      vim.keymap.set("v", prompt.mapping, function()
        M.execute_prompt(key)
      end, { desc = prompt.title })
    end
  end
  
  -- 其他工作流快捷键
  vim.keymap.set("v", "<leader>Aba", M.batch_analysis, { desc = "批量代码分析" })
  vim.keymap.set("v", "<leader>Asr", M.smart_refactor, { desc = "智能重构建议" })
  vim.keymap.set("n", "<leader>Apd", M.generate_project_docs, { desc = "生成项目文档" })
end

-- 添加用户命令
vim.api.nvim_create_user_command("AIWorkflowStats", function()
  M.run_diagnostics()
end, { desc = "显示 AI 工作流统计" })

vim.api.nvim_create_user_command("AIWorkflowReset", M.reset_stats, {
  desc = "重置 AI 工作流统计",
})

vim.api.nvim_create_user_command("AIWorkflowExport", M.export_stats, {
  desc = "导出 AI 工作流统计",
})

-- 初始化
setup_keymaps()

return M
