-- ~/.config/lvim/lua/user/languages/c_cpp.lua
-- C/C++ 语言开发配置

-- 确保 clangd 可用
lvim.lsp.automatic_configuration.skipped_servers = vim.tbl_filter(function(server)
  return server ~= "clangd"
end, lvim.lsp.automatic_configuration.skipped_servers)

-- C/C++ 特定设置
vim.api.nvim_create_autocmd("FileType", {
  pattern = { "c", "cpp", "h", "hpp" },
  callback = function()
    vim.opt_local.tabstop = 4
    vim.opt_local.shiftwidth = 4
    vim.opt_local.expandtab = true
    vim.opt_local.textwidth = 80
    vim.opt_local.colorcolumn = "80"
    
    -- C/C++ 特定选项
    vim.opt_local.cindent = true
    vim.opt_local.cinoptions = ":0,l1,t0,g0,(0"
  end,
})

-- C/C++ 工具配置
local formatters = require("lvim.lsp.null-ls.formatters")
formatters.setup({
  { command = "clang_format", filetypes = { "c", "cpp" } },
})

-- C/C++ 调试配置
local dap = require("dap")

-- GDB/LLDB 调试器配置
dap.adapters.gdb = {
  type = "executable",
  command = "gdb",
  args = { "-i", "dap" },
}

dap.adapters.lldb = {
  type = "executable",
  command = "/usr/bin/lldb-vscode", -- Arch Linux 路径
  name = "lldb",
}

-- C 调试配置
dap.configurations.c = {
  {
    name = "Launch (GDB)",
    type = "gdb",
    request = "launch",
    program = function()
      return vim.fn.input("可执行文件路径: ", vim.fn.getcwd() .. "/", "file")
    end,
    cwd = "${workspaceFolder}",
    stopAtBeginningOfMainSubprogram = false,
  },
  {
    name = "Launch (LLDB)",
    type = "lldb",
    request = "launch",
    program = function()
      return vim.fn.input("可执行文件路径: ", vim.fn.getcwd() .. "/", "file")
    end,
    cwd = "${workspaceFolder}",
    stopOnEntry = false,
    args = {},
  },
}

-- C++ 调试配置（复用 C 的配置）
dap.configurations.cpp = dap.configurations.c

-- C/C++ 快捷键配置
lvim.builtin.which_key.mappings["lc"] = {
  name = "C/C++ 开发",
  c = { "<cmd>!gcc % -o %:r<CR>", "编译 C 文件" },
  C = { "<cmd>!g++ % -o %:r<CR>", "编译 C++ 文件" },
  r = { "<cmd>!./%:r<CR>", "运行可执行文件" },
  d = { "<cmd>!gcc -g % -o %:r<CR>", "调试编译 C" },
  D = { "<cmd>!g++ -g % -o %:r<CR>", "调试编译 C++" },
  m = { "<cmd>!make<CR>", "Make 构建" },
  M = { "<cmd>!make clean<CR>", "清理构建" },
  f = { "<cmd>!clang-format -i %<CR>", "格式化代码" },
  v = { "<cmd>!valgrind ./%:r<CR>", "内存检查" },
  s = { "<cmd>!objdump -d %:r<CR>", "反汇编" },
  h = { "<cmd>!man ", "查看手册" },
}

-- C/C++ 代码片段
local luasnip = require("luasnip")
local s = luasnip.snippet
local t = luasnip.text_node
local i = luasnip.insert_node

-- C 代码片段
luasnip.add_snippets("c", {
  s("main", {
    t({"#include <stdio.h>", "", "int main() {"}),
    t({"", "    "}),
    i(1, "printf(\"Hello, World!\\n\");"),
    t({"", "    return 0;", "}"}),
  }),
  
  s("func", {
    i(1, "int"),
    t(" "),
    i(2, "function_name"),
    t("("),
    i(3, ""),
    t(") {"),
    t({"", "    "}),
    i(4, "return 0;"),
    t({"", "}"}),
  }),
  
  s("for", {
    t("for (int "),
    i(1, "i"),
    t(" = 0; "),
    i(1, "i"),
    t(" < "),
    i(2, "n"),
    t("; "),
    i(1, "i"),
    t("++) {"),
    t({"", "    "}),
    i(3, ""),
    t({"", "}"}),
  }),
  
  s("if", {
    t("if ("),
    i(1, "condition"),
    t(") {"),
    t({"", "    "}),
    i(2, ""),
    t({"", "}"}),
  }),
})

-- C++ 代码片段
luasnip.add_snippets("cpp", {
  s("main", {
    t({"#include <iostream>", "", "int main() {"}),
    t({"", "    "}),
    i(1, "std::cout << \"Hello, World!\" << std::endl;"),
    t({"", "    return 0;", "}"}),
  }),
  
  s("class", {
    t("class "),
    i(1, "ClassName"),
    t(" {"),
    t({"", "public:"}),
    t({"", "    "}),
    i(2, "ClassName();"),
    t({"", "    ~"}),
    i(1, "ClassName"),
    t("();"),
    t({"", "", "private:"}),
    t({"", "    "}),
    i(3, ""),
    t({"", "};"}),
  }),
  
  s("namespace", {
    t("namespace "),
    i(1, "name"),
    t(" {"),
    t({"", "", "}  // namespace "}),
    i(1, "name"),
  }),
  
  s("template", {
    t("template<"),
    i(1, "typename T"),
    t(">"),
    t({"", ""}),
    i(2, "class ClassName"),
    t(" {"),
    t({"", "    "}),
    i(3, ""),
    t({"", "};"}),
  }),
})

-- C/C++ 项目模板
local function create_c_project()
  local project_name = vim.fn.input("项目名称: ")
  if project_name == "" then
    return
  end
  
  local language = vim.fn.input("语言 (c/cpp): ", "c")
  local build_system = vim.fn.input("构建系统 (make/cmake): ", "make")
  
  local commands = {
    "mkdir -p " .. project_name,
    "cd " .. project_name,
    "mkdir -p src include",
  }
  
  if language == "cpp" then
    table.insert(commands, "echo '#include <iostream>\n\nint main() {\n    std::cout << \"Hello, " .. project_name .. "!\" << std::endl;\n    return 0;\n}' > src/main.cpp")
  else
    table.insert(commands, "echo '#include <stdio.h>\n\nint main() {\n    printf(\"Hello, " .. project_name .. "!\\n\");\n    return 0;\n}' > src/main.c")
  end
  
  if build_system == "cmake" then
    local cmake_content = string.format([[cmake_minimum_required(VERSION 3.10)
project(%s)

set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 17)

include_directories(include)

file(GLOB SOURCES "src/*.c" "src/*.cpp")

add_executable(%s ${SOURCES})]], project_name, project_name)
    
    table.insert(commands, "echo '" .. cmake_content .. "' > CMakeLists.txt")
  else
    local makefile_content
    if language == "cpp" then
      makefile_content = string.format([[CC=g++
CFLAGS=-Wall -Wextra -std=c++17 -Iinclude
TARGET=%s
SRCDIR=src
SOURCES=$(wildcard $(SRCDIR)/*.cpp)
OBJECTS=$(SOURCES:.cpp=.o)

$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $(TARGET)

%%.o: %%.cpp
	$(CC) $(CFLAGS) -c $< -o $@

clean:
	rm -f $(OBJECTS) $(TARGET)

.PHONY: clean]], project_name)
    else
      makefile_content = string.format([[CC=gcc
CFLAGS=-Wall -Wextra -std=c11 -Iinclude
TARGET=%s
SRCDIR=src
SOURCES=$(wildcard $(SRCDIR)/*.c)
OBJECTS=$(SOURCES:.c=.o)

$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $(TARGET)

%%.o: %%.c
	$(CC) $(CFLAGS) -c $< -o $@

clean:
	rm -f $(OBJECTS) $(TARGET)

.PHONY: clean]], project_name)
    end
    
    table.insert(commands, "echo '" .. makefile_content .. "' > Makefile")
  end
  
  -- 创建 .gitignore
  table.insert(commands, "echo '*.o\n*.out\n*.exe\n" .. project_name .. "\nbuild/\n.vscode/\n.clangd/' > .gitignore")
  
  for _, cmd in ipairs(commands) do
    vim.fn.system(cmd)
  end
  
  vim.notify("C/C++ 项目 '" .. project_name .. "' 创建成功！", vim.log.levels.INFO)
end

-- 添加项目创建命令
vim.api.nvim_create_user_command("CNewProject", create_c_project, {
  desc = "创建新的 C/C++ 项目",
})

-- C/C++ 工具安装检查
local function check_c_tools()
  local tools = {
    { name = "gcc", desc = "GCC 编译器" },
    { name = "g++", desc = "G++ 编译器" },
    { name = "clang", desc = "Clang 编译器" },
    { name = "clang++", desc = "Clang++ 编译器" },
    { name = "clangd", desc = "Clangd LSP 服务器" },
    { name = "clang-format", desc = "代码格式化工具" },
    { name = "gdb", desc = "GDB 调试器" },
    { name = "lldb", desc = "LLDB 调试器" },
    { name = "make", desc = "Make 构建工具" },
    { name = "cmake", desc = "CMake 构建系统" },
    { name = "valgrind", desc = "内存检查工具" },
  }
  
  local missing = {}
  for _, tool in ipairs(tools) do
    if vim.fn.executable(tool.name) == 0 then
      table.insert(missing, tool.desc .. " (" .. tool.name .. ")")
    end
  end
  
  if #missing > 0 then
    vim.notify("缺少 C/C++ 工具:\n" .. table.concat(missing, "\n"), vim.log.levels.WARN)
    vim.notify("请运行: sudo pacman -S gcc clang lldb gdb make cmake valgrind", vim.log.levels.INFO)
  else
    vim.notify("✅ 所有 C/C++ 工具已安装", vim.log.levels.INFO)
  end
end

-- 添加工具检查命令
vim.api.nvim_create_user_command("CCheckTools", check_c_tools, {
  desc = "检查 C/C++ 工具安装状态",
})
