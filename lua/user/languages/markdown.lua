-- ~/.config/lvim/lua/user/languages/markdown.lua
-- Markdown 语言开发配置

-- Markdown 特定设置
vim.api.nvim_create_autocmd("FileType", {
  pattern = { "markdown", "md" },
  callback = function()
    vim.opt_local.wrap = true
    vim.opt_local.linebreak = true
    vim.opt_local.spell = true
    vim.opt_local.spelllang = "en,cjk"
    vim.opt_local.conceallevel = 2
    vim.opt_local.textwidth = 80
    vim.opt_local.colorcolumn = "80"
    
    -- 启用软换行
    vim.opt_local.breakindent = true
    vim.opt_local.showbreak = "↪ "
  end,
})

-- Markdown 工具配置
local formatters = require("lvim.lsp.null-ls.formatters")
formatters.setup({
  { command = "prettier", filetypes = { "markdown" } },
})

local linters = require("lvim.lsp.null-ls.linters")
linters.setup({
  { command = "markdownlint", filetypes = { "markdown" } },
})

-- Markdown 快捷键配置
lvim.builtin.which_key.mappings["lm"] = {
  name = "Markdown 编辑",
  p = { "<cmd>MarkdownPreview<CR>", "预览 Markdown" },
  s = { "<cmd>MarkdownPreviewStop<CR>", "停止预览" },
  t = { "<cmd>MarkdownPreviewToggle<CR>", "切换预览" },
  f = { "<cmd>!prettier --write %<CR>", "格式化文档" },
  l = { "<cmd>!markdownlint %<CR>", "Lint 检查" },
  c = { "<cmd>!pandoc % -o %:r.pdf<CR>", "转换为 PDF" },
  h = { "<cmd>!pandoc % -o %:r.html<CR>", "转换为 HTML" },
}

-- Markdown 代码片段
local luasnip = require("luasnip")
local s = luasnip.snippet
local t = luasnip.text_node
local i = luasnip.insert_node

luasnip.add_snippets("markdown", {
  s("link", {
    t("["),
    i(1, "text"),
    t("]("),
    i(2, "url"),
    t(")"),
  }),
  
  s("img", {
    t("!["),
    i(1, "alt text"),
    t("]("),
    i(2, "image url"),
    t(")"),
  }),
  
  s("code", {
    t("```"),
    i(1, "language"),
    t({"", ""}),
    i(2, "code"),
    t({"", "```"}),
  }),
  
  s("table", {
    t("| "),
    i(1, "Header 1"),
    t(" | "),
    i(2, "Header 2"),
    t(" |"),
    t({"", "| --- | --- |"}),
    t({"", "| "}),
    i(3, "Cell 1"),
    t(" | "),
    i(4, "Cell 2"),
    t(" |"),
  }),
  
  s("todo", {
    t("- [ ] "),
    i(1, "task"),
  }),
  
  s("done", {
    t("- [x] "),
    i(1, "completed task"),
  }),
})

-- Markdown 文件模板
vim.api.nvim_create_autocmd("BufNewFile", {
  pattern = "*.md",
  callback = function()
    local filename = vim.fn.expand("%:t:r")
    local lines = {
      "# " .. filename:gsub("_", " "):gsub("^%l", string.upper),
      "",
      "## 简介",
      "",
      "",
      "",
      "## 内容",
      "",
      "",
      "",
      "## 总结",
      "",
      "",
    }
    vim.api.nvim_buf_set_lines(0, 0, -1, false, lines)
    vim.api.nvim_win_set_cursor(0, {4, 0})
  end,
})

-- Markdown 增强功能
local function insert_date()
  local date = os.date("%Y-%m-%d")
  vim.api.nvim_put({date}, "c", true, true)
end

local function insert_time()
  local time = os.date("%H:%M:%S")
  vim.api.nvim_put({time}, "c", true, true)
end

local function insert_datetime()
  local datetime = os.date("%Y-%m-%d %H:%M:%S")
  vim.api.nvim_put({datetime}, "c", true, true)
end

-- 添加日期时间插入命令
vim.api.nvim_create_user_command("MarkdownInsertDate", insert_date, {
  desc = "插入当前日期",
})

vim.api.nvim_create_user_command("MarkdownInsertTime", insert_time, {
  desc = "插入当前时间",
})

vim.api.nvim_create_user_command("MarkdownInsertDateTime", insert_datetime, {
  desc = "插入当前日期时间",
})

-- Markdown 工具检查
local function check_markdown_tools()
  local tools = {
    { name = "prettier", desc = "Prettier 格式化工具" },
    { name = "markdownlint", desc = "Markdown Lint 工具" },
    { name = "pandoc", desc = "Pandoc 转换工具" },
  }
  
  local missing = {}
  for _, tool in ipairs(tools) do
    if vim.fn.executable(tool.name) == 0 then
      table.insert(missing, tool.desc .. " (" .. tool.name .. ")")
    end
  end
  
  if #missing > 0 then
    vim.notify("缺少 Markdown 工具:\n" .. table.concat(missing, "\n"), vim.log.levels.WARN)
    vim.notify("请运行: npm install -g prettier markdownlint-cli && sudo pacman -S pandoc", vim.log.levels.INFO)
  else
    vim.notify("✅ 所有 Markdown 工具已安装", vim.log.levels.INFO)
  end
end

-- 添加工具检查命令
vim.api.nvim_create_user_command("MarkdownCheckTools", check_markdown_tools, {
  desc = "检查 Markdown 工具安装状态",
})
