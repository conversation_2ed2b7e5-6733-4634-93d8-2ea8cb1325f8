-- ~/.config/lvim/lua/user/languages/rust.lua
-- Rust 语言开发配置

-- 确保 rust-analyzer 可用
lvim.lsp.automatic_configuration.skipped_servers = vim.tbl_filter(function(server)
  return server ~= "rust_analyzer"
end, lvim.lsp.automatic_configuration.skipped_servers)

-- Rust 特定设置
vim.api.nvim_create_autocmd("FileType", {
  pattern = "rust",
  callback = function()
    vim.opt_local.tabstop = 4
    vim.opt_local.shiftwidth = 4
    vim.opt_local.expandtab = true
    vim.opt_local.textwidth = 100
    vim.opt_local.colorcolumn = "100"
  end,
})

-- Rust 工具配置
local formatters = require("lvim.lsp.null-ls.formatters")
formatters.setup({
  { command = "rustfmt", filetypes = { "rust" } },
})

-- Rust 调试配置
local dap = require("dap")

-- LLDB 调试器配置
dap.adapters.lldb = {
  type = "executable",
  command = "/usr/bin/lldb-vscode", -- Arch Linux 路径
  name = "lldb",
}

dap.configurations.rust = {
  {
    name = "Launch",
    type = "lldb",
    request = "launch",
    program = function()
      return vim.fn.input("可执行文件路径: ", vim.fn.getcwd() .. "/target/debug/", "file")
    end,
    cwd = "${workspaceFolder}",
    stopOnEntry = false,
    args = {},
    runInTerminal = false,
  },
  {
    name = "Attach to process",
    type = "lldb",
    request = "attach",
    pid = require("dap.utils").pick_process,
    args = {},
  },
}

-- Rust 快捷键配置
lvim.builtin.which_key.mappings["lr"] = {
  name = "Rust 开发",
  r = { "<cmd>!cargo run<CR>", "运行项目" },
  b = { "<cmd>!cargo build<CR>", "构建项目" },
  t = { "<cmd>!cargo test<CR>", "运行测试" },
  T = { "<cmd>!cargo test -- --nocapture<CR>", "详细测试输出" },
  c = { "<cmd>!cargo check<CR>", "快速检查" },
  f = { "<cmd>!cargo fmt<CR>", "格式化代码" },
  l = { "<cmd>!cargo clippy<CR>", "Clippy 检查" },
  L = { "<cmd>!cargo clippy -- -D warnings<CR>", "严格 Clippy 检查" },
  d = { "<cmd>!cargo doc --open<CR>", "生成并打开文档" },
  u = { "<cmd>!cargo update<CR>", "更新依赖" },
  a = { "<cmd>!cargo add ", "添加依赖" },
  R = { "<cmd>!cargo run --release<CR>", "发布模式运行" },
  B = { "<cmd>!cargo build --release<CR>", "发布模式构建" },
}

-- Rust 代码片段
local luasnip = require("luasnip")
local s = luasnip.snippet
local t = luasnip.text_node
local i = luasnip.insert_node

luasnip.add_snippets("rust", {
  s("main", {
    t({"fn main() {"}),
    t({"", "    "}),
    i(1, "println!(\"Hello, world!\");"),
    t({"", "}"}),
  }),
  
  s("fn", {
    t("fn "),
    i(1, "name"),
    t("("),
    i(2, ""),
    t(") "),
    i(3, ""),
    t(" {"),
    t({"", "    "}),
    i(4, ""),
    t({"", "}"}),
  }),
  
  s("struct", {
    t("struct "),
    i(1, "Name"),
    t(" {"),
    t({"", "    "}),
    i(2, "field: Type,"),
    t({"", "}"}),
  }),
  
  s("impl", {
    t("impl "),
    i(1, "Type"),
    t(" {"),
    t({"", "    "}),
    i(2, ""),
    t({"", "}"}),
  }),
  
  s("test", {
    t({"#[cfg(test)]", "mod tests {", "    use super::*;", "", "    #[test]", "    fn "}),
    i(1, "test_name"),
    t("() {"),
    t({"", "        "}),
    i(2, "assert_eq!(1, 1);"),
    t({"", "    }", "}"}),
  }),
})

-- Rust 项目模板
local function create_rust_project()
  local project_name = vim.fn.input("项目名称: ")
  if project_name == "" then
    return
  end
  
  local project_type = vim.fn.input("项目类型 (bin/lib): ", "bin")
  
  local cmd = "cargo new " .. project_name
  if project_type == "lib" then
    cmd = cmd .. " --lib"
  end
  
  vim.fn.system(cmd)
  
  if vim.v.shell_error == 0 then
    vim.notify("Rust 项目 '" .. project_name .. "' 创建成功！", vim.log.levels.INFO)
    
    -- 询问是否打开项目
    local open = vim.fn.input("是否打开项目? (y/n): ", "y")
    if open:lower() == "y" then
      vim.cmd("cd " .. project_name)
      if project_type == "lib" then
        vim.cmd("edit src/lib.rs")
      else
        vim.cmd("edit src/main.rs")
      end
    end
  else
    vim.notify("项目创建失败！", vim.log.levels.ERROR)
  end
end

-- 添加项目创建命令
vim.api.nvim_create_user_command("RustNewProject", create_rust_project, {
  desc = "创建新的 Rust 项目",
})

-- Rust 文件模板
vim.api.nvim_create_autocmd("BufNewFile", {
  pattern = "*.rs",
  callback = function()
    local filename = vim.fn.expand("%:t:r")
    local lines
    
    if filename == "main" then
      lines = {
        "fn main() {",
        "    println!(\"Hello, world!\");",
        "}",
      }
    elseif filename == "lib" then
      lines = {
        "//! " .. vim.fn.expand("%:p:h:t") .. " library",
        "",
        "pub fn add(left: usize, right: usize) -> usize {",
        "    left + right",
        "}",
        "",
        "#[cfg(test)]",
        "mod tests {",
        "    use super::*;",
        "",
        "    #[test]",
        "    fn it_works() {",
        "        let result = add(2, 2);",
        "        assert_eq!(result, 4);",
        "    }",
        "}",
      }
    else
      lines = {
        "// " .. filename .. ".rs",
        "",
      }
    end
    
    vim.api.nvim_buf_set_lines(0, 0, -1, false, lines)
    vim.api.nvim_win_set_cursor(0, {2, 4}) -- 定位到合适位置
  end,
})

-- Rust 工具安装检查
local function check_rust_tools()
  local tools = {
    { name = "rustc", desc = "Rust 编译器" },
    { name = "cargo", desc = "Rust 包管理器" },
    { name = "rustfmt", desc = "代码格式化工具" },
    { name = "clippy", desc = "代码检查工具" },
    { name = "rust-analyzer", desc = "LSP 服务器" },
  }
  
  local missing = {}
  for _, tool in ipairs(tools) do
    if vim.fn.executable(tool.name) == 0 then
      table.insert(missing, tool.desc .. " (" .. tool.name .. ")")
    end
  end
  
  if #missing > 0 then
    vim.notify("缺少 Rust 工具:\n" .. table.concat(missing, "\n"), vim.log.levels.WARN)
    vim.notify("请运行: rustup component add rustfmt clippy rust-analyzer", vim.log.levels.INFO)
  else
    vim.notify("✅ 所有 Rust 工具已安装", vim.log.levels.INFO)
  end
end

-- 添加工具检查命令
vim.api.nvim_create_user_command("RustCheckTools", check_rust_tools, {
  desc = "检查 Rust 工具安装状态",
})
