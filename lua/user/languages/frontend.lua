-- ~/.config/lvim/lua/user/languages/frontend.lua
-- 前端开发语言配置 (JavaScript/TypeScript/Vue/React)

-- 确保前端 LSP 服务器可用
local skipped_servers = {
  "tsserver", "eslint", "volar", "tailwindcss"
}

for _, server in ipairs(skipped_servers) do
  lvim.lsp.automatic_configuration.skipped_servers = vim.tbl_filter(function(s)
    return s ~= server
  end, lvim.lsp.automatic_configuration.skipped_servers)
end

-- JavaScript/TypeScript 特定设置
vim.api.nvim_create_autocmd("FileType", {
  pattern = { "javascript", "typescript", "javascriptreact", "typescriptreact" },
  callback = function()
    vim.opt_local.tabstop = 2
    vim.opt_local.shiftwidth = 2
    vim.opt_local.expandtab = true
    vim.opt_local.textwidth = 100
    vim.opt_local.colorcolumn = "100"
  end,
})

-- Vue 特定设置
vim.api.nvim_create_autocmd("FileType", {
  pattern = "vue",
  callback = function()
    vim.opt_local.tabstop = 2
    vim.opt_local.shiftwidth = 2
    vim.opt_local.expandtab = true
    vim.opt_local.textwidth = 100
    vim.opt_local.colorcolumn = "100"
  end,
})

-- CSS/SCSS 特定设置
vim.api.nvim_create_autocmd("FileType", {
  pattern = { "css", "scss", "sass", "less" },
  callback = function()
    vim.opt_local.tabstop = 2
    vim.opt_local.shiftwidth = 2
    vim.opt_local.expandtab = true
  end,
})

-- 前端工具配置
local formatters = require("lvim.lsp.null-ls.formatters")
formatters.setup({
  { command = "prettier", filetypes = { "javascript", "typescript", "javascriptreact", "typescriptreact", "vue", "css", "scss", "html", "json" } },
  { command = "eslint_d", filetypes = { "javascript", "typescript", "javascriptreact", "typescriptreact", "vue" } },
})

local linters = require("lvim.lsp.null-ls.linters")
linters.setup({
  { command = "eslint_d", filetypes = { "javascript", "typescript", "javascriptreact", "typescriptreact", "vue" } },
  { command = "stylelint", filetypes = { "css", "scss", "sass", "less" } },
})

-- 前端快捷键配置
lvim.builtin.which_key.mappings["lf"] = {
  name = "前端开发",
  
  -- JavaScript/TypeScript
  j = {
    name = "JavaScript/TypeScript",
    r = { "<cmd>!node %<CR>", "运行 JS 文件" },
    t = { "<cmd>!npm test<CR>", "运行测试" },
    b = { "<cmd>!npm run build<CR>", "构建项目" },
    d = { "<cmd>!npm run dev<CR>", "开发模式" },
    s = { "<cmd>!npm start<CR>", "启动项目" },
    i = { "<cmd>!npm install<CR>", "安装依赖" },
    f = { "<cmd>!prettier --write %<CR>", "格式化文件" },
    l = { "<cmd>!eslint %<CR>", "ESLint 检查" },
  },
  
  -- Vue.js
  v = {
    name = "Vue.js",
    r = { "<cmd>!npm run serve<CR>", "运行 Vue 项目" },
    b = { "<cmd>!npm run build<CR>", "构建 Vue 项目" },
    t = { "<cmd>!npm run test:unit<CR>", "单元测试" },
    e = { "<cmd>!npm run test:e2e<CR>", "E2E 测试" },
    l = { "<cmd>!npm run lint<CR>", "代码检查" },
  },
  
  -- React
  r = {
    name = "React",
    s = { "<cmd>!npm start<CR>", "启动 React 应用" },
    b = { "<cmd>!npm run build<CR>", "构建 React 应用" },
    t = { "<cmd>!npm test<CR>", "运行测试" },
    e = { "<cmd>!npm run eject<CR>", "弹出配置" },
  },
  
  -- CSS/样式
  c = {
    name = "CSS/样式",
    f = { "<cmd>!prettier --write %<CR>", "格式化 CSS" },
    l = { "<cmd>!stylelint %<CR>", "CSS Lint" },
    s = { "<cmd>!sass % %:r.css<CR>", "编译 SASS" },
  },
  
  -- 包管理
  p = {
    name = "包管理",
    i = { "<cmd>!npm install ", "安装包" },
    u = { "<cmd>!npm uninstall ", "卸载包" },
    o = { "<cmd>!npm outdated<CR>", "检查过期包" },
    a = { "<cmd>!npm audit<CR>", "安全审计" },
    f = { "<cmd>!npm audit fix<CR>", "修复安全问题" },
  },
}

-- 前端代码片段
local luasnip = require("luasnip")
local s = luasnip.snippet
local t = luasnip.text_node
local i = luasnip.insert_node

-- JavaScript 代码片段
luasnip.add_snippets("javascript", {
  s("func", {
    t("function "),
    i(1, "name"),
    t("("),
    i(2, ""),
    t(") {"),
    t({"", "  "}),
    i(3, ""),
    t({"", "}"}),
  }),
  
  s("arrow", {
    t("const "),
    i(1, "name"),
    t(" = ("),
    i(2, ""),
    t(") => {"),
    t({"", "  "}),
    i(3, ""),
    t({"", "}"}),
  }),
  
  s("async", {
    t("async function "),
    i(1, "name"),
    t("("),
    i(2, ""),
    t(") {"),
    t({"", "  "}),
    i(3, ""),
    t({"", "}"}),
  }),
  
  s("promise", {
    t("new Promise((resolve, reject) => {"),
    t({"", "  "}),
    i(1, ""),
    t({"", "})"}),
  }),
})

-- TypeScript 代码片段
luasnip.add_snippets("typescript", {
  s("interface", {
    t("interface "),
    i(1, "Name"),
    t(" {"),
    t({"", "  "}),
    i(2, "property: type;"),
    t({"", "}"}),
  }),
  
  s("type", {
    t("type "),
    i(1, "Name"),
    t(" = "),
    i(2, "string"),
    t(";"),
  }),
  
  s("class", {
    t("class "),
    i(1, "Name"),
    t(" {"),
    t({"", "  constructor("}),
    i(2, ""),
    t(") {"),
    t({"", "    "}),
    i(3, ""),
    t({"", "  }", "}"}),
  }),
})

-- Vue 代码片段
luasnip.add_snippets("vue", {
  s("template", {
    t("<template>"),
    t({"", "  <div>"}),
    t({"", "    "}),
    i(1, ""),
    t({"", "  </div>"}),
    t({"", "</template>"}),
    t({"", "", "<script>"}),
    t({"", "export default {"}),
    t({"", "  name: '"}),
    i(2, "ComponentName"),
    t("',"),
    t({"", "  "}),
    i(3, ""),
    t({"", "}"}),
    t({"", "</script>"}),
    t({"", "", "<style scoped>"}),
    t({"", "}"}),
    t({"", "</style>"}),
  }),
})

-- React 代码片段
luasnip.add_snippets("javascriptreact", {
  s("component", {
    t("import React from 'react';"),
    t({"", "", "const "}),
    i(1, "ComponentName"),
    t(" = () => {"),
    t({"", "  return ("}),
    t({"", "    <div>"}),
    t({"", "      "}),
    i(2, ""),
    t({"", "    </div>"}),
    t({"", "  );"}),
    t({"", "};"}),
    t({"", "", "export default "}),
    i(1, "ComponentName"),
    t(";"),
  }),
  
  s("useState", {
    t("const ["),
    i(1, "state"),
    t(", set"),
    i(1, "State"),
    t("] = useState("),
    i(2, "initialValue"),
    t(");"),
  }),
  
  s("useEffect", {
    t("useEffect(() => {"),
    t({"", "  "}),
    i(1, ""),
    t({"", "}, ["}),
    i(2, ""),
    t("]);"),
  }),
})

-- 前端项目模板
local function create_frontend_project()
  local project_name = vim.fn.input("项目名称: ")
  if project_name == "" then
    return
  end
  
  local framework = vim.fn.input("框架 (vue/react/vanilla): ", "vue")
  
  local commands = {}
  
  if framework == "vue" then
    table.insert(commands, "npm create vue@latest " .. project_name)
  elseif framework == "react" then
    table.insert(commands, "npx create-react-app " .. project_name)
  else
    -- Vanilla JavaScript 项目
    table.insert(commands, "mkdir -p " .. project_name)
    table.insert(commands, "cd " .. project_name)
    table.insert(commands, "npm init -y")
    table.insert(commands, "echo '<!DOCTYPE html>\n<html>\n<head>\n  <title>" .. project_name .. "</title>\n</head>\n<body>\n  <h1>Hello, " .. project_name .. "!</h1>\n  <script src=\"script.js\"></script>\n</body>\n</html>' > index.html")
    table.insert(commands, "echo 'console.log(\"Hello, " .. project_name .. "!\");' > script.js")
    table.insert(commands, "echo 'body { font-family: Arial, sans-serif; }' > style.css")
  end
  
  for _, cmd in ipairs(commands) do
    vim.fn.system(cmd)
  end
  
  vim.notify("前端项目 '" .. project_name .. "' 创建成功！", vim.log.levels.INFO)
end

-- 添加项目创建命令
vim.api.nvim_create_user_command("FrontendNewProject", create_frontend_project, {
  desc = "创建新的前端项目",
})

-- 前端工具检查
local function check_frontend_tools()
  local tools = {
    { name = "node", desc = "Node.js" },
    { name = "npm", desc = "NPM 包管理器" },
    { name = "prettier", desc = "代码格式化工具" },
    { name = "eslint", desc = "JavaScript Lint 工具" },
    { name = "stylelint", desc = "CSS Lint 工具" },
  }
  
  local missing = {}
  for _, tool in ipairs(tools) do
    if vim.fn.executable(tool.name) == 0 then
      table.insert(missing, tool.desc .. " (" .. tool.name .. ")")
    end
  end
  
  if #missing > 0 then
    vim.notify("缺少前端工具:\n" .. table.concat(missing, "\n"), vim.log.levels.WARN)
    vim.notify("请运行: sudo pacman -S nodejs npm && npm install -g prettier eslint stylelint", vim.log.levels.INFO)
  else
    vim.notify("✅ 所有前端工具已安装", vim.log.levels.INFO)
  end
end

-- 添加工具检查命令
vim.api.nvim_create_user_command("FrontendCheckTools", check_frontend_tools, {
  desc = "检查前端工具安装状态",
})
