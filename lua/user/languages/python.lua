-- ~/.config/lvim/lua/user/languages/python.lua
-- Python 语言开发配置

-- 确保 Python LSP 可用
lvim.lsp.automatic_configuration.skipped_servers = vim.tbl_filter(function(server)
  return server ~= "pyright" and server ~= "pylsp"
end, lvim.lsp.automatic_configuration.skipped_servers)

-- Python 特定设置
vim.api.nvim_create_autocmd("FileType", {
  pattern = "python",
  callback = function()
    vim.opt_local.tabstop = 4
    vim.opt_local.shiftwidth = 4
    vim.opt_local.expandtab = true
    vim.opt_local.textwidth = 88 -- Black 格式化器的默认宽度
    vim.opt_local.colorcolumn = "88"
    
    -- Python 特定选项
    vim.opt_local.foldmethod = "indent"
    vim.opt_local.foldlevel = 99
  end,
})

-- Python 工具配置
local formatters = require("lvim.lsp.null-ls.formatters")
formatters.setup({
  { command = "black", filetypes = { "python" } },
  { command = "isort", filetypes = { "python" } },
})

local linters = require("lvim.lsp.null-ls.linters")
linters.setup({
  { command = "flake8", filetypes = { "python" } },
  { command = "mypy", filetypes = { "python" } },
})

-- Python 调试配置
local dap = require("dap")

-- Python 调试器配置
dap.adapters.python = function(cb, config)
  if config.request == "attach" then
    local port = (config.connect or config).port
    local host = (config.connect or config).host or "127.0.0.1"
    cb({
      type = "server",
      port = assert(port, "`connect.port` is required for a python `attach` configuration"),
      host = host,
      options = {
        source_filetype = "python",
      },
    })
  else
    cb({
      type = "executable",
      command = "python",
      args = { "-m", "debugpy.adapter" },
      options = {
        source_filetype = "python",
      },
    })
  end
end

dap.configurations.python = {
  {
    type = "python",
    request = "launch",
    name = "Launch file",
    program = "${file}",
    pythonPath = function()
      local cwd = vim.fn.getcwd()
      if vim.fn.executable(cwd .. "/venv/bin/python") == 1 then
        return cwd .. "/venv/bin/python"
      elseif vim.fn.executable(cwd .. "/.venv/bin/python") == 1 then
        return cwd .. "/.venv/bin/python"
      else
        return "/usr/bin/python"
      end
    end,
  },
  {
    type = "python",
    request = "launch",
    name = "Launch module",
    module = function()
      return vim.fn.input("Module name: ")
    end,
    pythonPath = function()
      local cwd = vim.fn.getcwd()
      if vim.fn.executable(cwd .. "/venv/bin/python") == 1 then
        return cwd .. "/venv/bin/python"
      elseif vim.fn.executable(cwd .. "/.venv/bin/python") == 1 then
        return cwd .. "/.venv/bin/python"
      else
        return "/usr/bin/python"
      end
    end,
  },
}

-- Python 快捷键配置
lvim.builtin.which_key.mappings["lp"] = {
  name = "Python 开发",
  r = { "<cmd>!python %<CR>", "运行当前文件" },
  R = { "<cmd>!python -m ", "运行模块" },
  t = { "<cmd>!python -m pytest<CR>", "运行测试" },
  T = { "<cmd>!python -m pytest -v<CR>", "详细测试" },
  c = { "<cmd>!python -m py_compile %<CR>", "编译检查" },
  f = { "<cmd>!black %<CR>", "格式化当前文件" },
  F = { "<cmd>!black .<CR>", "格式化项目" },
  i = { "<cmd>!isort %<CR>", "整理导入" },
  I = { "<cmd>!isort .<CR>", "整理项目导入" },
  l = { "<cmd>!flake8 %<CR>", "Lint 检查" },
  L = { "<cmd>!flake8 .<CR>", "项目 Lint 检查" },
  m = { "<cmd>!mypy %<CR>", "类型检查" },
  M = { "<cmd>!mypy .<CR>", "项目类型检查" },
  v = { "<cmd>!python -m venv venv<CR>", "创建虚拟环境" },
  a = { "<cmd>source venv/bin/activate<CR>", "激活虚拟环境" },
  p = { "<cmd>!pip install ", "安装包" },
  P = { "<cmd>!pip freeze > requirements.txt<CR>", "导出依赖" },
}

-- Python 代码片段
local luasnip = require("luasnip")
local s = luasnip.snippet
local t = luasnip.text_node
local i = luasnip.insert_node

luasnip.add_snippets("python", {
  s("main", {
    t({"if __name__ == \"__main__\":"}),
    t({"", "    "}),
    i(1, "main()"),
  }),
  
  s("def", {
    t("def "),
    i(1, "function_name"),
    t("("),
    i(2, ""),
    t(") -> "),
    i(3, "None"),
    t(":"),
    t({"", "    \"\"\""}),
    i(4, "Function description."),
    t({"", "    \"\"\""}),
    t({"", "    "}),
    i(5, "pass"),
  }),
  
  s("class", {
    t("class "),
    i(1, "ClassName"),
    t("("),
    i(2, ""),
    t("):"),
    t({"", "    \"\"\""}),
    i(3, "Class description."),
    t({"", "    \"\"\""}),
    t({"", "", "    def __init__(self"}),
    i(4, ""),
    t("):"),
    t({"", "        "}),
    i(5, "pass"),
  }),
  
  s("try", {
    t("try:"),
    t({"", "    "}),
    i(1, "pass"),
    t({"", "except "}),
    i(2, "Exception"),
    t(" as e:"),
    t({"", "    "}),
    i(3, "print(f\"Error: {e}\")"),
  }),
  
  s("test", {
    t("def test_"),
    i(1, "function_name"),
    t("():"),
    t({"", "    \"\"\"Test "}),
    i(2, "description"),
    t(".\"\"\""),
    t({"", "    "}),
    i(3, "assert True"),
  }),
})

-- Python 项目模板
local function create_python_project()
  local project_name = vim.fn.input("项目名称: ")
  if project_name == "" then
    return
  end
  
  local project_type = vim.fn.input("项目类型 (package/script/web): ", "package")
  
  -- 创建项目目录结构
  local commands = {
    "mkdir -p " .. project_name,
    "cd " .. project_name,
  }
  
  if project_type == "package" then
    table.insert(commands, "mkdir -p " .. project_name)
    table.insert(commands, "mkdir -p tests")
    table.insert(commands, "touch " .. project_name .. "/__init__.py")
    table.insert(commands, "touch tests/__init__.py")
    table.insert(commands, "touch tests/test_" .. project_name .. ".py")
    table.insert(commands, "echo '# " .. project_name .. "\n\n## 安装\n\n```bash\npip install -e .\n```\n\n## 使用\n\n```python\nimport " .. project_name .. "\n```' > README.md")
    table.insert(commands, "echo '[build-system]\nrequires = [\"setuptools>=45\", \"wheel\"]\nbuild-backend = \"setuptools.build_meta\"\n\n[project]\nname = \"" .. project_name .. "\"\nversion = \"0.1.0\"\ndescription = \"A Python package\"\nauthors = [{name = \"Your Name\", email = \"<EMAIL>\"}]\nrequires-python = \">=3.8\"\ndependencies = []\n\n[project.optional-dependencies]\ndev = [\"pytest\", \"black\", \"isort\", \"flake8\", \"mypy\"]' > pyproject.toml")
  elseif project_type == "web" then
    table.insert(commands, "echo 'flask\nrequests' > requirements.txt")
    table.insert(commands, "echo 'from flask import Flask\n\napp = Flask(__name__)\n\<EMAIL>(\"/\")\ndef hello():\n    return \"Hello, World!\"\n\nif __name__ == \"__main__\":\n    app.run(debug=True)' > app.py")
  else
    table.insert(commands, "touch main.py")
    table.insert(commands, "echo '#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\ndef main():\n    print(\"Hello, " .. project_name .. "!\")\n\nif __name__ == \"__main__\":\n    main()' > main.py")
  end
  
  -- 通用文件
  table.insert(commands, "echo '__pycache__/\n*.py[cod]\n*$py.class\n*.so\n.Python\nbuild/\ndevelop-eggs/\ndist/\ndownloads/\neggs/\n.eggs/\nlib/\nlib64/\nparts/\nsdist/\nvar/\nwheels/\n*.egg-info/\n.installed.cfg\n*.egg\nPIPFILE.lock\n.env\n.venv\nenv/\nvenv/\nENV/\nenv.bak/\nvenv.bak/' > .gitignore")
  
  for _, cmd in ipairs(commands) do
    vim.fn.system(cmd)
  end
  
  vim.notify("Python 项目 '" .. project_name .. "' 创建成功！", vim.log.levels.INFO)
end

-- 添加项目创建命令
vim.api.nvim_create_user_command("PythonNewProject", create_python_project, {
  desc = "创建新的 Python 项目",
})

-- Python 工具安装检查
local function check_python_tools()
  local tools = {
    { name = "python", desc = "Python 解释器" },
    { name = "pip", desc = "包管理器" },
    { name = "black", desc = "代码格式化工具" },
    { name = "isort", desc = "导入排序工具" },
    { name = "flake8", desc = "代码检查工具" },
    { name = "mypy", desc = "类型检查工具" },
    { name = "pytest", desc = "测试框架" },
  }
  
  local missing = {}
  for _, tool in ipairs(tools) do
    if vim.fn.executable(tool.name) == 0 then
      table.insert(missing, tool.desc .. " (" .. tool.name .. ")")
    end
  end
  
  if #missing > 0 then
    vim.notify("缺少 Python 工具:\n" .. table.concat(missing, "\n"), vim.log.levels.WARN)
    vim.notify("请运行: pip install black isort flake8 mypy pytest debugpy", vim.log.levels.INFO)
  else
    vim.notify("✅ 所有 Python 工具已安装", vim.log.levels.INFO)
  end
end

-- 添加工具检查命令
vim.api.nvim_create_user_command("PythonCheckTools", check_python_tools, {
  desc = "检查 Python 工具安装状态",
})
