-- ~/.config/lvim/lua/user/languages/init.lua
-- 语言支持统一入口

local languages = {
  "golang",
  "rust", 
  "python",
  "c_cpp",
  "markdown",
  "frontend",
}

-- 加载语言配置
for _, lang in ipairs(languages) do
  local ok, err = pcall(require, "user.languages." .. lang)
  if not ok then
    vim.notify("Failed to load language config: " .. lang .. " - " .. err, vim.log.levels.WARN)
  else
    vim.notify("✅ Loaded language config: " .. lang, vim.log.levels.DEBUG)
  end
end

-- 通用语言设置
vim.api.nvim_create_autocmd("FileType", {
  pattern = "*",
  callback = function()
    -- 为所有文件类型启用基础功能
    vim.opt_local.conceallevel = 0
    vim.opt_local.concealcursor = ""
  end,
})

-- LSP 通用配置
local function setup_lsp_keymaps(bufnr)
  local opts = { buffer = bufnr, silent = true }
  
  -- LSP 快捷键
  vim.keymap.set("n", "gd", vim.lsp.buf.definition, opts)
  vim.keymap.set("n", "gD", vim.lsp.buf.declaration, opts)
  vim.keymap.set("n", "gr", vim.lsp.buf.references, opts)
  vim.keymap.set("n", "gi", vim.lsp.buf.implementation, opts)
  vim.keymap.set("n", "K", vim.lsp.buf.hover, opts)
  vim.keymap.set("n", "<C-k>", vim.lsp.buf.signature_help, opts)
  vim.keymap.set("n", "<leader>rn", vim.lsp.buf.rename, opts)
  vim.keymap.set("n", "<leader>ca", vim.lsp.buf.code_action, opts)
  vim.keymap.set("n", "<leader>f", function()
    vim.lsp.buf.format({ async = true })
  end, opts)
end

-- LSP 附加时自动设置快捷键
vim.api.nvim_create_autocmd("LspAttach", {
  callback = function(ev)
    setup_lsp_keymaps(ev.buf)
  end,
})

-- 诊断配置
vim.diagnostic.config({
  virtual_text = {
    prefix = "●",
    source = "if_many",
  },
  float = {
    source = "always",
    border = "rounded",
  },
  signs = true,
  underline = true,
  update_in_insert = false,
  severity_sort = true,
})

-- 诊断符号
local signs = {
  Error = " ",
  Warn = " ",
  Hint = " ",
  Info = " ",
}

for type, icon in pairs(signs) do
  local hl = "DiagnosticSign" .. type
  vim.fn.sign_define(hl, { text = icon, texthl = hl, numhl = hl })
end
