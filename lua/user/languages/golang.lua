-- ~/.config/lvim/lua/user/languages/golang.lua
-- Go 语言开发配置

-- 确保 gopls 可用
lvim.lsp.automatic_configuration.skipped_servers = vim.tbl_filter(function(server)
  return server ~= "gopls"
end, lvim.lsp.automatic_configuration.skipped_servers)

-- Go 特定设置
vim.api.nvim_create_autocmd("FileType", {
  pattern = "go",
  callback = function()
    -- Go 使用 tab 缩进
    vim.opt_local.tabstop = 4
    vim.opt_local.shiftwidth = 4
    vim.opt_local.expandtab = false
    vim.opt_local.list = true
    vim.opt_local.listchars = "tab:▸ ,trail:·"
    
    -- Go 特定选项
    vim.opt_local.textwidth = 100
    vim.opt_local.colorcolumn = "100"
  end,
})

-- Go 工具配置
local formatters = require("lvim.lsp.null-ls.formatters")
formatters.setup({
  { command = "gofmt", filetypes = { "go" } },
  { command = "goimports", filetypes = { "go" } },
})

local linters = require("lvim.lsp.null-ls.linters")
linters.setup({
  { command = "golangci_lint", filetypes = { "go" } },
})

-- Go 调试配置
local dap = require("dap")

-- Delve 调试器配置
dap.adapters.go = function(callback, config)
  local stdout = vim.loop.new_pipe(false)
  local handle, pid_or_err
  local port = 38697

  handle, pid_or_err = vim.loop.spawn("dlv", {
    stdio = { nil, stdout, nil },
    args = { "dap", "-l", "127.0.0.1:" .. port },
    detached = true,
  }, function(code)
    stdout:close()
    handle:close()
    if code ~= 0 then
      print("dlv exited with code", code)
    end
  end)

  vim.defer_fn(function()
    callback({ type = "server", host = "127.0.0.1", port = port })
  end, 100)
end

-- Go 调试配置
dap.configurations.go = {
  {
    type = "go",
    name = "Debug",
    request = "launch",
    program = "${file}",
  },
  {
    type = "go",
    name = "Debug Package",
    request = "launch",
    program = "${fileDirname}",
  },
  {
    type = "go",
    name = "Debug Test",
    request = "launch",
    mode = "test",
    program = "${workspaceFolder}",
  },
  {
    type = "go",
    name = "Attach",
    request = "attach",
    mode = "remote",
    remotePath = "${workspaceFolder}",
    port = 2345,
    host = "127.0.0.1",
  },
}

-- Go 快捷键配置
lvim.builtin.which_key.mappings["lg"] = {
  name = "Go 开发",
  r = { "<cmd>!go run %<CR>", "运行当前文件" },
  b = { "<cmd>!go build<CR>", "构建项目" },
  t = { "<cmd>!go test<CR>", "运行测试" },
  T = { "<cmd>!go test -v<CR>", "详细测试" },
  c = { "<cmd>!go test -cover<CR>", "测试覆盖率" },
  f = { "<cmd>!gofmt -w %<CR>", "格式化当前文件" },
  i = { "<cmd>!goimports -w %<CR>", "整理导入" },
  l = { "<cmd>!golangci-lint run<CR>", "Lint 检查" },
  d = { "<cmd>!go doc<CR>", "查看文档" },
  m = { "<cmd>!go mod tidy<CR>", "整理模块" },
  v = { "<cmd>!go vet<CR>", "代码检查" },
}

-- Go 代码片段
local luasnip = require("luasnip")
local s = luasnip.snippet
local t = luasnip.text_node
local i = luasnip.insert_node

luasnip.add_snippets("go", {
  s("main", {
    t({"package main", "", "import (", "\t\"fmt\"", ")", "", "func main() {"}),
    t({"", "\t"}),
    i(1, "fmt.Println(\"Hello, World!\")"),
    t({"", "}"}),
  }),
  
  s("func", {
    t("func "),
    i(1, "name"),
    t("("),
    i(2, ""),
    t(") "),
    i(3, ""),
    t(" {"),
    t({"", "\t"}),
    i(4, ""),
    t({"", "}"}),
  }),
  
  s("if", {
    t("if "),
    i(1, "condition"),
    t(" {"),
    t({"", "\t"}),
    i(2, ""),
    t({"", "}"}),
  }),
  
  s("for", {
    t("for "),
    i(1, "i := 0; i < 10; i++"),
    t(" {"),
    t({"", "\t"}),
    i(2, ""),
    t({"", "}"}),
  }),
})

-- Go 项目模板
local function create_go_project()
  local project_name = vim.fn.input("项目名称: ")
  if project_name == "" then
    return
  end
  
  local commands = {
    "mkdir -p " .. project_name,
    "cd " .. project_name,
    "go mod init " .. project_name,
    "echo 'package main\n\nimport \"fmt\"\n\nfunc main() {\n\tfmt.Println(\"Hello, " .. project_name .. "!\")\n}' > main.go",
  }
  
  for _, cmd in ipairs(commands) do
    vim.fn.system(cmd)
  end
  
  vim.notify("Go 项目 '" .. project_name .. "' 创建成功！", vim.log.levels.INFO)
end

-- 添加项目创建命令
vim.api.nvim_create_user_command("GoNewProject", create_go_project, {
  desc = "创建新的 Go 项目",
})

-- Go 文件模板
vim.api.nvim_create_autocmd("BufNewFile", {
  pattern = "*.go",
  callback = function()
    local lines = {
      "package main",
      "",
      "import (",
      "\t\"fmt\"",
      ")",
      "",
      "func main() {",
      "\tfmt.Println(\"Hello, World!\")",
      "}",
    }
    vim.api.nvim_buf_set_lines(0, 0, -1, false, lines)
    vim.api.nvim_win_set_cursor(0, {8, 1}) -- 定位到 main 函数内
  end,
})
