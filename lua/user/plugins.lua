-- ~/.config/lvim/lua/user/plugins.lua
-- 此文件用于管理您的所有插件

lvim.plugins = {
  {
  "kevinhwang91/rnvimr", -- 集成 ranger 文件管理器
    cmd = "RnvimrToggle",
    config = function()
      vim.g.rnvimr_draw_border = 1
      vim.g.rnvimr_pick_enable = 1
      vim.g.rnvimr_bw_enable = 1
      end,
  },
  {
    "zbirenbaum/copilot.lua", -- GitHub Copilot 插件
    cmd = "Copilot",
    event = "InsertEnter",
    config = function()
      require("copilot").setup({
        suggestion = { auto_trigger = true },
        panel = { auto_refresh = true },
      })
    end,
  },
  {
    "zbirenbaum/copilot-cmp", -- Copilot 与 cmp 补全集成
    dependencies = { "zbirenbaum/copilot.lua" },
    config = function()
      require("copilot_cmp").setup()
    end,
  },
  {
    "CopilotC-Nvim/CopilotChat.nvim", -- Copilot Chat 插件
    dependencies = { "zbirenbaum/copilot.lua", "nvim-lua/plenary.nvim" },
    opts = { model = "gpt-4o" },
  },
  {
    "olimorris/codecompanion.nvim",
    dependencies = { "nvim-lua/plenary.nvim", "nvim-telescope/telescope.nvim" },
    config = function()
      require("codecompanion").setup({
        default_provider = "openai",
        providers = {
          openai = { api_key = vim.fn.expand("$OPENAI_API_KEY"), model = "gpt-4o" },
          anthropic = { api_key = vim.fn.expand("$ANTHROPIC_API_KEY"), model = "claude-3-opus-20240229" },
          google = { api_key = vim.fn.expand("$GOOGLE_API_KEY"), model = "gemini-1.5-pro-latest" },
        },
      })
    end,
  },
  -- MCP 协议支持
  {
    "ravitemer/mcphub.nvim",
    dependencies = {
      "nvim-lua/plenary.nvim",
      "nvim-telescope/telescope.nvim",
    },
    config = function()
      -- 确保PATH包含npm全局目录
      local npm_bin = vim.fn.expand("~/.npm-global/bin")
      local current_path = os.getenv("PATH") or ""
      if not current_path:match(npm_bin) then
        vim.fn.setenv("PATH", npm_bin .. ":" .. current_path)
      end

      -- 加载 MCP 诊断工具
      require("user.ai.mcp_diagnostics")

      -- MCP 配置在 user.ai.mcp 模块中处理
      vim.notify("MCP Hub 插件已加载", vim.log.levels.DEBUG)
    end,
    cond = function()
      -- 确保PATH包含npm全局目录
      local npm_bin = vim.fn.expand("~/.npm-global/bin")
      local current_path = os.getenv("PATH") or ""
      if not current_path:match(npm_bin) then
        vim.fn.setenv("PATH", npm_bin .. ":" .. current_path)
      end

      -- 检查mcp-hub是否可用
      return vim.fn.executable("mcp-hub") == 1
    end,
  },

  -- 您原有的其他插件...
  { "yetone/avante.nvim" },
  { "augmentcode/augment.vim" },
}
