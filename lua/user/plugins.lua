-- ~/.config/lvim/lua/user/plugins.lua
-- 此文件用于管理您的所有插件

lvim.plugins = {
  {
  "kevinhwang91/rnvimr", -- 集成 ranger 文件管理器
    cmd = "RnvimrToggle",
    config = function()
      vim.g.rnvimr_draw_border = 1
      vim.g.rnvimr_pick_enable = 1
      vim.g.rnvimr_bw_enable = 1
      end,
  },
  {
    "zbirenbaum/copilot.lua", -- GitHub Copilot 插件
    cmd = "Copilot",
    event = "InsertEnter",
    config = function()
      require("copilot").setup({
        suggestion = { auto_trigger = true },
        panel = { auto_refresh = true },
      })
    end,
  },
  {
    "zbirenbaum/copilot-cmp", -- Copilot 与 cmp 补全集成
    dependencies = { "zbirenbaum/copilot.lua" },
    config = function()
      require("copilot_cmp").setup()
    end,
  },
  {
    "CopilotC-Nvim/CopilotChat.nvim", -- Copilot Chat 插件
    dependencies = { "zbirenbaum/copilot.lua", "nvim-lua/plenary.nvim" },
    opts = { model = "gpt-4o" },
  },
  {
    "olimorris/codecompanion.nvim",
    dependencies = { "nvim-lua/plenary.nvim", "nvim-telescope/telescope.nvim" },
    config = function()
      require("codecompanion").setup({
        default_provider = "openai",
        providers = {
          openai = { api_key = vim.fn.expand("$OPENAI_API_KEY"), model = "gpt-4o" },
          anthropic = { api_key = vim.fn.expand("$ANTHROPIC_API_KEY"), model = "claude-3-opus-20240229" },
          google = { api_key = vim.fn.expand("$GOOGLE_API_KEY"), model = "gemini-1.5-pro-latest" },
        },
      })
    end,
  },
  -- Avante.nvim AI 助手插件 (类似 Cursor AI IDE)
  {
    "yetone/avante.nvim",
    event = "VeryLazy",
    version = false, -- 不要设置为 "*"
    build = function()
      -- 根据操作系统选择正确的构建系统
      if vim.fn.has("win32") == 1 then
        return "powershell -ExecutionPolicy Bypass -File Build.ps1 -BuildFromSource false"
      else
        return "make"
      end
    end,
    dependencies = {
      "nvim-lua/plenary.nvim",
      "MunifTanjim/nui.nvim",
      -- 可选依赖
      "nvim-telescope/telescope.nvim", -- 文件选择器
      "hrsh7th/nvim-cmp", -- 自动补全
      "nvim-tree/nvim-web-devicons", -- 图标
      "zbirenbaum/copilot.lua", -- Copilot 支持
      {
        -- 图片粘贴支持
        "HakonHarnes/img-clip.nvim",
        event = "VeryLazy",
        opts = {
          default = {
            embed_image_as_base64 = false,
            prompt_for_file_name = false,
            drag_and_drop = {
              insert_mode = true,
            },
            use_absolute_path = true,
          },
        },
      },
      {
        -- Markdown 渲染支持
        'MeanderingProgrammer/render-markdown.nvim',
        opts = {
          file_types = { "markdown", "Avante" },
        },
        ft = { "markdown", "Avante" },
      },
    },
    config = function()
      require("avante").setup({
        -- 基本配置
        provider = "claude", -- 默认 AI 提供商
        auto_suggestions_provider = "claude",

        -- 提供商配置
        providers = {
          claude = {
            endpoint = "https://api.anthropic.com",
            model = "claude-3-5-sonnet-20241022",
            timeout = 30000,
            extra_request_body = {
              temperature = 0.75,
              max_tokens = 4096,
            },
          },
          openai = {
            endpoint = "https://api.openai.com/v1",
            model = "gpt-4o",
            timeout = 30000,
            extra_request_body = {
              temperature = 0.75,
              max_tokens = 4096,
            },
          },
          gemini = {
            endpoint = "https://generativelanguage.googleapis.com/v1beta/models",
            model = "gemini-1.5-pro-latest",
            timeout = 30000,
            extra_request_body = {
              temperature = 0.75,
            },
          },
        },

        -- 行为配置
        behaviour = {
          auto_suggestions = false, -- 实验性功能
          auto_set_highlight_group = true,
          auto_set_keymaps = true,
          auto_apply_diff_after_generation = false,
          support_paste_from_clipboard = false,
          minimize_diff = true,
        },

        -- 窗口配置
        windows = {
          position = "right", -- 侧边栏位置
          wrap = true,
          width = 30, -- 宽度百分比
          sidebar_header = {
            enabled = true,
            align = "center",
            rounded = true,
          },
        },

        -- 快捷键映射
        mappings = {
          diff = {
            ours = "co",
            theirs = "ct",
            all_theirs = "ca",
            both = "cb",
            cursor = "cc",
            next = "]x",
            prev = "[x",
          },
          suggestion = {
            accept = "<M-l>",
            next = "<M-]>",
            prev = "<M-[>",
            dismiss = "<C-]>",
          },
          jump = {
            next = "]]",
            prev = "[[",
          },
          submit = {
            normal = "<CR>",
            insert = "<C-s>",
          },
          sidebar = {
            apply_all = "A",
            apply_cursor = "a",
            retry_user_request = "r",
            edit_user_request = "e",
            switch_windows = "<Tab>",
            reverse_switch_windows = "<S-Tab>",
            remove_file = "d",
            add_file = "@",
            close = { "<Esc>", "q" },
          },
        },

        -- 提示和高亮
        hints = { enabled = true },
        highlights = {
          diff = {
            current = "DiffText",
            incoming = "DiffAdd",
          },
        },
      })
    end,
  },

  -- MCP (Model Context Protocol) 支持
  {
    "ravitemer/mcphub.nvim",
    event = "VeryLazy",
    cond = function()
      -- 只有在 npm 和 npx 可用时才加载
      return vim.fn.executable("npm") == 1 and vim.fn.executable("npx") == 1
    end,
    config = function()
      -- MCP 配置将在 ai/mcp.lua 中处理
      vim.notify("🔗 mcphub.nvim 插件已加载", vim.log.levels.DEBUG)
    end,
  },

  -- Augment AI 插件
  { "augmentcode/augment.vim" },
}
