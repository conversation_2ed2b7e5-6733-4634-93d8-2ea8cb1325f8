#!/bin/bash
# ~/.config/lvim/install_ai_tools.sh
# LunarVim AI 工具自动安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 Arch Linux
check_arch_linux() {
    if [[ ! -f /etc/arch-release ]]; then
        log_warning "此脚本专为 Arch Linux 设计，其他发行版可能需要调整"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 安装 Arch Linux 包
install_arch_packages() {
    log_info "安装 Arch Linux 系统包..."
    
    local packages=(
        # 编译器和构建工具
        "gcc" "clang" "make" "cmake"
        # 调试器
        "gdb" "lldb"
        # 代码格式化和检查工具
        "clang-format"
        # 内存检查工具
        "valgrind"
        # Node.js 和 npm (用于 MCP 服务器)
        "nodejs" "npm"
        # Python 工具
        "python-pip"
        # Rust 工具链
        "rustup"
    )
    
    for package in "${packages[@]}"; do
        if ! pacman -Qi "$package" >/dev/null 2>&1; then
            log_info "安装 $package..."
            sudo pacman -S --noconfirm "$package"
        else
            log_success "$package 已安装"
        fi
    done
}

# 安装 AUR 包
install_aur_packages() {
    log_info "检查 AUR 助手..."
    
    if ! command_exists yay && ! command_exists paru; then
        log_warning "未找到 AUR 助手，请手动安装 yay 或 paru"
        return 1
    fi
    
    local aur_helper="yay"
    if command_exists paru; then
        aur_helper="paru"
    fi
    
    local aur_packages=(
        # 可选的 AUR 包
    )
    
    for package in "${aur_packages[@]}"; do
        log_info "安装 AUR 包: $package..."
        $aur_helper -S --noconfirm "$package" || log_warning "安装 $package 失败"
    done
}

# 设置 Rust 工具链
setup_rust() {
    log_info "设置 Rust 工具链..."
    
    if command_exists rustup; then
        # 安装默认工具链
        rustup default stable
        
        # 安装必要组件
        rustup component add rustfmt clippy rust-analyzer
        
        log_success "Rust 工具链设置完成"
    else
        log_error "rustup 未安装"
        return 1
    fi
}

# 安装 Python 工具
install_python_tools() {
    log_info "安装 Python AI 工具..."
    
    local python_packages=(
        "black"          # 代码格式化
        "isort"          # 导入排序
        "flake8"         # 代码检查
        "mypy"           # 类型检查
        "pytest"         # 测试框架
        "debugpy"        # 调试器
        "pylsp"          # LSP 服务器
    )
    
    for package in "${python_packages[@]}"; do
        log_info "安装 Python 包: $package..."
        pip install --user "$package" || log_warning "安装 $package 失败"
    done
    
    log_success "Python 工具安装完成"
}

# 安装 Node.js MCP 服务器
install_mcp_servers() {
    log_info "安装 MCP 服务器..."
    
    if ! command_exists npm; then
        log_error "npm 未安装，无法安装 MCP 服务器"
        return 1
    fi
    
    local mcp_servers=(
        "@modelcontextprotocol/server-filesystem"
        "@modelcontextprotocol/server-git"
        "@modelcontextprotocol/server-time"
    )
    
    for server in "${mcp_servers[@]}"; do
        log_info "安装 MCP 服务器: $server..."
        npm install -g "$server" || log_warning "安装 $server 失败"
    done
    
    log_success "MCP 服务器安装完成"
}

# 安装 Go 工具
install_go_tools() {
    log_info "检查 Go 工具..."
    
    if ! command_exists go; then
        log_info "安装 Go..."
        sudo pacman -S --noconfirm go
    fi
    
    if command_exists go; then
        log_info "安装 Go 工具..."
        
        # 安装 Go 工具
        go install golang.org/x/tools/gopls@latest
        go install golang.org/x/tools/cmd/goimports@latest
        go install github.com/go-delve/delve/cmd/dlv@latest
        go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
        
        log_success "Go 工具安装完成"
    fi
}

# 创建环境变量模板
create_env_template() {
    log_info "创建环境变量配置模板..."
    
    local env_file="$HOME/.ai_keys_template.sh"
    
    cat > "$env_file" << 'EOF'
#!/bin/bash
# AI API 密钥配置模板
# 复制此文件为 ~/.ai_keys.sh 并填入真实的 API 密钥

# OpenAI API 密钥
export OPENAI_API_KEY='sk-your_openai_key_here'

# Anthropic Claude API 密钥
export ANTHROPIC_API_KEY='sk-ant-your_anthropic_key_here'

# Google Gemini API 密钥
export GEMINI_API_KEY='your_gemini_key_here'

# GitHub Token
export GITHUB_TOKEN='ghp_your_github_token_here'

# Augment API 密钥
export AUGMENT_API_KEY='your_augment_key_here'

# 在 ~/.bashrc 或 ~/.zshrc 中添加:
# source ~/.ai_keys.sh
EOF
    
    chmod +x "$env_file"
    log_success "环境变量模板已创建: $env_file"
}

# 配置 shell 环境
setup_shell_env() {
    log_info "配置 shell 环境..."
    
    local shell_rc=""
    if [[ "$SHELL" == *"zsh"* ]]; then
        shell_rc="$HOME/.zshrc"
    elif [[ "$SHELL" == *"bash"* ]]; then
        shell_rc="$HOME/.bashrc"
    else
        log_warning "未知的 shell: $SHELL"
        return 1
    fi
    
    # 添加 Rust 环境
    if ! grep -q 'source $HOME/.cargo/env' "$shell_rc" 2>/dev/null; then
        echo 'source $HOME/.cargo/env' >> "$shell_rc"
        log_info "已添加 Rust 环境到 $shell_rc"
    fi
    
    # 添加 Go 环境
    if ! grep -q 'export PATH=$PATH:$(go env GOPATH)/bin' "$shell_rc" 2>/dev/null; then
        echo 'export PATH=$PATH:$(go env GOPATH)/bin' >> "$shell_rc"
        log_info "已添加 Go 环境到 $shell_rc"
    fi
    
    # 添加 Python 用户包路径
    if ! grep -q 'export PATH=$PATH:$HOME/.local/bin' "$shell_rc" 2>/dev/null; then
        echo 'export PATH=$PATH:$HOME/.local/bin' >> "$shell_rc"
        log_info "已添加 Python 用户包路径到 $shell_rc"
    fi
    
    log_success "Shell 环境配置完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    local tools=(
        "gcc:GCC 编译器"
        "clang:Clang 编译器"
        "rustc:Rust 编译器"
        "go:Go 编译器"
        "python:Python 解释器"
        "node:Node.js"
        "npm:NPM 包管理器"
        "black:Python 格式化工具"
        "rustfmt:Rust 格式化工具"
        "gofmt:Go 格式化工具"
    )
    
    local missing=()
    
    for tool_info in "${tools[@]}"; do
        IFS=':' read -r tool desc <<< "$tool_info"
        if command_exists "$tool"; then
            log_success "$desc 已安装"
        else
            log_warning "$desc 未找到"
            missing+=("$desc")
        fi
    done
    
    if [[ ${#missing[@]} -eq 0 ]]; then
        log_success "所有工具验证通过！"
    else
        log_warning "以下工具未安装: ${missing[*]}"
    fi
}

# 显示后续步骤
show_next_steps() {
    log_info "安装完成！后续步骤:"
    echo
    echo "1. 重新加载 shell 配置:"
    echo "   source ~/.zshrc  # 或 source ~/.bashrc"
    echo
    echo "2. 配置 API 密钥:"
    echo "   cp ~/.ai_keys_template.sh ~/.ai_keys.sh"
    echo "   编辑 ~/.ai_keys.sh 填入真实的 API 密钥"
    echo "   在 ~/.zshrc 中添加: source ~/.ai_keys.sh"
    echo
    echo "3. 重启 LunarVim 测试配置:"
    echo "   lvim"
    echo
    echo "4. 在 LunarVim 中运行健康检查:"
    echo "   :AIKeysStatus"
    echo "   :LvimInfo"
    echo "   :Mason"
    echo
}

# 主函数
main() {
    log_info "开始安装 LunarVim AI 工具..."
    
    check_arch_linux
    install_arch_packages
    install_aur_packages
    setup_rust
    install_python_tools
    install_go_tools
    install_mcp_servers
    create_env_template
    setup_shell_env
    verify_installation
    show_next_steps
    
    log_success "安装脚本执行完成！"
}

# 运行主函数
main "$@"
