lvim.builtin.alpha.active = true
lvim.builtin.alpha.dashboard.opts.autostart = true
lvim.builtin.alpha.dashboard.section.buttons.entries["1"]["1"] = "f"
lvim.builtin.alpha.dashboard.section.buttons.entries["1"]["2"] = "󰈞  Find File"
lvim.builtin.alpha.dashboard.section.buttons.entries["1"]["3"] = "<CMD>Telescope find_files<CR>"
lvim.builtin.alpha.dashboard.section.buttons.entries["2"]["1"] = "n"
lvim.builtin.alpha.dashboard.section.buttons.entries["2"]["2"] = "  New File"
lvim.builtin.alpha.dashboard.section.buttons.entries["2"]["3"] = "<CMD>ene!<CR>"
lvim.builtin.alpha.dashboard.section.buttons.entries["3"]["1"] = "p"
lvim.builtin.alpha.dashboard.section.buttons.entries["3"]["2"] = "  Projects "
lvim.builtin.alpha.dashboard.section.buttons.entries["3"]["3"] = "<CMD>Telescope projects<CR>"
lvim.builtin.alpha.dashboard.section.buttons.entries["4"]["1"] = "r"
lvim.builtin.alpha.dashboard.section.buttons.entries["4"]["2"] = "  Recent files"
lvim.builtin.alpha.dashboard.section.buttons.entries["4"]["3"] = ":Telescope oldfiles <CR>"
lvim.builtin.alpha.dashboard.section.buttons.entries["5"]["1"] = "t"
lvim.builtin.alpha.dashboard.section.buttons.entries["5"]["2"] = "󰊄  Find Text"
lvim.builtin.alpha.dashboard.section.buttons.entries["5"]["3"] = "<CMD>Telescope live_grep<CR>"
lvim.builtin.alpha.dashboard.section.buttons.entries["6"]["1"] = "c"
lvim.builtin.alpha.dashboard.section.buttons.entries["6"]["2"] = "  Configuration"
lvim.builtin.alpha.dashboard.section.buttons.entries["6"]["3"] = "<CMD>edit /home/<USER>/.config/lvim/config.lua <CR>"
lvim.builtin.alpha.dashboard.section.buttons.entries["7"]["1"] = "q"
lvim.builtin.alpha.dashboard.section.buttons.entries["7"]["2"] = "󰅖  Quit"
lvim.builtin.alpha.dashboard.section.buttons.entries["7"]["3"] = "<CMD>quit<CR>"
lvim.builtin.alpha.dashboard.section.buttons.opts.hl_shortcut = "Include"
lvim.builtin.alpha.dashboard.section.buttons.opts.spacing = 1
lvim.builtin.alpha.dashboard.section.footer.opts.hl = "Number"
lvim.builtin.alpha.dashboard.section.footer.opts.position = "center"
lvim.builtin.alpha.dashboard.section.footer.type = "text"
lvim.builtin.alpha.dashboard.section.footer.val["1"] = "       "
lvim.builtin.alpha.dashboard.section.footer.val["2"] = " lunarvim.org"
lvim.builtin.alpha.dashboard.section.footer.val["3"] = "master-85ccca9"
lvim.builtin.alpha.dashboard.section.header.opts.hl = "Label"
lvim.builtin.alpha.dashboard.section.header.opts.position = "center"
lvim.builtin.alpha.dashboard.section.header.type = "text"
-- lvim.builtin.alpha.dashboard.section.header.val = function ()
lvim.builtin.alpha.mode = "dashboard"
lvim.builtin.alpha.startify.opts.autostart = true
lvim.builtin.alpha.startify.section.bottom_buttons.entries["1"]["1"] = "q"
lvim.builtin.alpha.startify.section.bottom_buttons.entries["1"]["2"] = "Quit"
lvim.builtin.alpha.startify.section.bottom_buttons.entries["1"]["3"] = "<CMD>quit<CR>"
lvim.builtin.alpha.startify.section.footer.type = "group"
lvim.builtin.alpha.startify.section.header.opts.hl = "Label"
lvim.builtin.alpha.startify.section.header.opts.shrink_margin = false
lvim.builtin.alpha.startify.section.header.type = "text"
lvim.builtin.alpha.startify.section.header.val["1"] = "    __                          _    ___         "
lvim.builtin.alpha.startify.section.header.val["2"] = "   / /   __  ______  ____ _____| |  / (_)___ ___ "
lvim.builtin.alpha.startify.section.header.val["3"] = "  / /   / / / / __ \\/ __ `/ ___/ | / / / __ `__ \\"
lvim.builtin.alpha.startify.section.header.val["4"] = " / /___/ /_/ / / / / /_/ / /   | |/ / / / / / / /"
lvim.builtin.alpha.startify.section.header.val["5"] = "/_____/\\__,_/_/ /_/\\__,_/_/    |___/_/_/ /_/ /_/ "
lvim.builtin.alpha.startify.section.top_buttons.entries["1"]["1"] = "e"
lvim.builtin.alpha.startify.section.top_buttons.entries["1"]["2"] = " New File"
lvim.builtin.alpha.startify.section.top_buttons.entries["1"]["3"] = "<CMD>ene!<CR>"
lvim.builtin.autopairs.active = true
lvim.builtin.autopairs.break_undo = true
lvim.builtin.autopairs.check_ts = true
lvim.builtin.autopairs.disable_filetype["1"] = "TelescopePrompt"
lvim.builtin.autopairs.disable_filetype["2"] = "spectre_panel"
lvim.builtin.autopairs.disable_in_macro = false
lvim.builtin.autopairs.disable_in_replace_mode = true
lvim.builtin.autopairs.disable_in_visualblock = false
lvim.builtin.autopairs.enable_abbr = false
lvim.builtin.autopairs.enable_afterquote = true
lvim.builtin.autopairs.enable_check_bracket_line = false
lvim.builtin.autopairs.enable_moveright = true
lvim.builtin.autopairs.fast_wrap.chars["1"] = "{"
lvim.builtin.autopairs.fast_wrap.chars["2"] = "["
lvim.builtin.autopairs.fast_wrap.chars["3"] = "("
lvim.builtin.autopairs.fast_wrap.chars["4"] = "\""
lvim.builtin.autopairs.fast_wrap.chars["5"] = "'"
lvim.builtin.autopairs.fast_wrap.check_comma = true
lvim.builtin.autopairs.fast_wrap.end_key = "$"
lvim.builtin.autopairs.fast_wrap.highlight_grey = "Comment"
lvim.builtin.autopairs.fast_wrap.highlight = "Search"
lvim.builtin.autopairs.fast_wrap.keys = "qwertyuiopzxcvbnmasdfghjkl"
lvim.builtin.autopairs.fast_wrap.map = "<M-e>"
lvim.builtin.autopairs.fast_wrap.offset = 0
lvim.builtin.autopairs.fast_wrap.pattern = "[%'%\"%)%>%]%)%}%,]"
lvim.builtin.autopairs.ignored_next_char = "[%w%%%'%[%\"%.]"
lvim.builtin.autopairs.map_bs = true
lvim.builtin.autopairs.map_char.all = "("
lvim.builtin.autopairs.map_char.tex = "{"
lvim.builtin.autopairs.map_c_h = false
lvim.builtin.autopairs.map_cr = true
lvim.builtin.autopairs.map_c_w = false
lvim.builtin.autopairs.ts_config.java = false
lvim.builtin.autopairs.ts_config.javascript["1"] = "string"
lvim.builtin.autopairs.ts_config.javascript["2"] = "template_string"
lvim.builtin.autopairs.ts_config.lua["1"] = "string"
lvim.builtin.autopairs.ts_config.lua["2"] = "source"
lvim.builtin.bigfile.active = true
lvim.builtin.breadcrumbs.active = true
lvim.builtin.breadcrumbs.options.depth_limit = 0
lvim.builtin.breadcrumbs.options.depth_limit_indicator = ".."
lvim.builtin.breadcrumbs.options.highlight = true
lvim.builtin.breadcrumbs.options.icons.Array = " "
lvim.builtin.breadcrumbs.options.icons.Boolean = " "
lvim.builtin.breadcrumbs.options.icons.Class = " "
lvim.builtin.breadcrumbs.options.icons.Color = " "
lvim.builtin.breadcrumbs.options.icons.Constant = " "
lvim.builtin.breadcrumbs.options.icons.Constructor = " "
lvim.builtin.breadcrumbs.options.icons.Enum = " "
lvim.builtin.breadcrumbs.options.icons.EnumMember = " "
lvim.builtin.breadcrumbs.options.icons.Event = " "
lvim.builtin.breadcrumbs.options.icons.Field = " "
lvim.builtin.breadcrumbs.options.icons.File = " "
lvim.builtin.breadcrumbs.options.icons.Folder = "󰉋 "
lvim.builtin.breadcrumbs.options.icons.Function = " "
lvim.builtin.breadcrumbs.options.icons.Interface = " "
lvim.builtin.breadcrumbs.options.icons.Key = " "
lvim.builtin.breadcrumbs.options.icons.Keyword = " "
lvim.builtin.breadcrumbs.options.icons.Method = " "
lvim.builtin.breadcrumbs.options.icons.Module = " "
lvim.builtin.breadcrumbs.options.icons.Namespace = " "
lvim.builtin.breadcrumbs.options.icons.Null = "󰟢 "
lvim.builtin.breadcrumbs.options.icons.Number = " "
lvim.builtin.breadcrumbs.options.icons.Object = " "
lvim.builtin.breadcrumbs.options.icons.Operator = " "
lvim.builtin.breadcrumbs.options.icons.Package = " "
lvim.builtin.breadcrumbs.options.icons.Property = " "
lvim.builtin.breadcrumbs.options.icons.Reference = " "
lvim.builtin.breadcrumbs.options.icons.Snippet = " "
lvim.builtin.breadcrumbs.options.icons.String = " "
lvim.builtin.breadcrumbs.options.icons.Struct = " "
lvim.builtin.breadcrumbs.options.icons.Text = " "
lvim.builtin.breadcrumbs.options.icons.TypeParameter = " "
lvim.builtin.breadcrumbs.options.icons.Unit = " "
lvim.builtin.breadcrumbs.options.icons.Value = " "
lvim.builtin.breadcrumbs.options.icons.Variable = " "
lvim.builtin.breadcrumbs.options.separator = "  "
lvim.builtin.breadcrumbs.winbar_filetype_exclude["10"] = "lir"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["11"] = "Outline"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["12"] = "spectre_panel"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["13"] = "toggleterm"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["14"] = "DressingSelect"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["15"] = "Jaq"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["16"] = "harpoon"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["17"] = "dap-repl"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["18"] = "dap-terminal"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["19"] = "dapui_console"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["1"] = "help"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["20"] = "dapui_hover"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["21"] = "lab"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["22"] = "notify"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["23"] = "noice"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["24"] = "neotest-summary"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["25"] = ""
lvim.builtin.breadcrumbs.winbar_filetype_exclude["2"] = "startify"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["3"] = "dashboard"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["4"] = "lazy"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["5"] = "neo-tree"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["6"] = "neogitstatus"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["7"] = "NvimTree"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["8"] = "Trouble"
lvim.builtin.breadcrumbs.winbar_filetype_exclude["9"] = "alpha"
lvim.builtin.bufferline.active = true
lvim.builtin.bufferline.highlights.background.italic = true
lvim.builtin.bufferline.highlights.buffer_selected.bold = true
lvim.builtin.bufferline.options.always_show_bufferline = false
lvim.builtin.bufferline.options.auto_toggle_bufferline = true
lvim.builtin.bufferline.options.buffer_close_icon = "󰅖"
-- lvim.builtin.bufferline.options.close_command = function ()
lvim.builtin.bufferline.options.close_icon = ""
lvim.builtin.bufferline.options.color_icons = true
-- lvim.builtin.bufferline.options.custom_filter = function ()
lvim.builtin.bufferline.options.debug.logging = false
-- lvim.builtin.bufferline.options.diagnostics_indicator = function ()
lvim.builtin.bufferline.options.diagnostics = "nvim_lsp"
lvim.builtin.bufferline.options.diagnostics_update_in_insert = false
lvim.builtin.bufferline.options.duplicates_across_groups = true
lvim.builtin.bufferline.options.enforce_regular_tabs = false
lvim.builtin.bufferline.options.groups.options.toggle_hidden_on_enter = true
lvim.builtin.bufferline.options.hover.delay = 200
lvim.builtin.bufferline.options.hover.enabled = false
lvim.builtin.bufferline.options.hover.reveal["1"] = "close"
lvim.builtin.bufferline.options.indicator.icon = "▎"
lvim.builtin.bufferline.options.indicator.style = "icon"
lvim.builtin.bufferline.options.left_mouse_command = "buffer %d"
lvim.builtin.bufferline.options.left_trunc_marker = ""
lvim.builtin.bufferline.options.max_name_length = 18
lvim.builtin.bufferline.options.max_prefix_length = 15
lvim.builtin.bufferline.options.mode = "buffers"
lvim.builtin.bufferline.options.modified_icon = " "
lvim.builtin.bufferline.options.move_wraps_at_ends = false
-- lvim.builtin.bufferline.options.name_formatter = function ()
lvim.builtin.bufferline.options.numbers = "none"
lvim.builtin.bufferline.options.offsets["1"].filetype = "undotree"
lvim.builtin.bufferline.options.offsets["1"].highlight = "PanelHeading"
lvim.builtin.bufferline.options.offsets["1"].padding = 1
lvim.builtin.bufferline.options.offsets["1"].text = "Undotree"
lvim.builtin.bufferline.options.offsets["2"].filetype = "NvimTree"
lvim.builtin.bufferline.options.offsets["2"].highlight = "PanelHeading"
lvim.builtin.bufferline.options.offsets["2"].padding = 1
lvim.builtin.bufferline.options.offsets["2"].text = "Explorer"
lvim.builtin.bufferline.options.offsets["3"].filetype = "DiffviewFiles"
lvim.builtin.bufferline.options.offsets["3"].highlight = "PanelHeading"
lvim.builtin.bufferline.options.offsets["3"].padding = 1
lvim.builtin.bufferline.options.offsets["3"].text = "Diff View"
lvim.builtin.bufferline.options.offsets["4"].filetype = "flutterToolsOutline"
lvim.builtin.bufferline.options.offsets["4"].highlight = "PanelHeading"
lvim.builtin.bufferline.options.offsets["4"].text = "Flutter Outline"
lvim.builtin.bufferline.options.offsets["5"].filetype = "lazy"
lvim.builtin.bufferline.options.offsets["5"].highlight = "PanelHeading"
lvim.builtin.bufferline.options.offsets["5"].padding = 1
lvim.builtin.bufferline.options.offsets["5"].text = "Lazy"
lvim.builtin.bufferline.options.persist_buffer_sort = true
lvim.builtin.bufferline.options.right_mouse_command = "vert sbuffer %d"
lvim.builtin.bufferline.options.right_trunc_marker = ""
lvim.builtin.bufferline.options.separator_style = "thin"
lvim.builtin.bufferline.options.show_buffer_close_icons = true
lvim.builtin.bufferline.options.show_buffer_icons = true
lvim.builtin.bufferline.options.show_close_icon = false
lvim.builtin.bufferline.options.show_duplicate_prefix = true
lvim.builtin.bufferline.options.show_tab_indicators = true
lvim.builtin.bufferline.options.sort_by = "id"
lvim.builtin.bufferline.options.tab_size = 18
lvim.builtin.bufferline.options.themable = true
lvim.builtin.bufferline.options.truncate_names = true
lvim.builtin.cmp.active = true
lvim.builtin.cmp.cmdline.enable = false
lvim.builtin.cmp.cmdline.options["1"].sources["1"].name = "path"
lvim.builtin.cmp.cmdline.options["1"].sources["2"].name = "cmdline"
lvim.builtin.cmp.cmdline.options["1"].type = ":"
lvim.builtin.cmp.cmdline.options["2"].sources["1"].name = "buffer"
lvim.builtin.cmp.cmdline.options["2"].type["1"] = "/"
lvim.builtin.cmp.cmdline.options["2"].type["2"] = "?"
lvim.builtin.cmp.completion.keyword_length = 1
lvim.builtin.cmp.confirm_opts.behavior = "replace"
lvim.builtin.cmp.confirm_opts.select = false
-- lvim.builtin.cmp.enabled = function ()
lvim.builtin.cmp.experimental.ghost_text = false
lvim.builtin.cmp.experimental.native_menu = false
lvim.builtin.cmp.formatting.duplicates.buffer = 1
lvim.builtin.cmp.formatting.duplicates_default = 0
lvim.builtin.cmp.formatting.duplicates.luasnip = 1
lvim.builtin.cmp.formatting.duplicates.nvim_lsp = 0
lvim.builtin.cmp.formatting.duplicates.path = 1
lvim.builtin.cmp.formatting.fields["1"] = "kind"
lvim.builtin.cmp.formatting.fields["2"] = "abbr"
lvim.builtin.cmp.formatting.fields["3"] = "menu"
-- lvim.builtin.cmp.formatting.format = function ()
lvim.builtin.cmp.formatting.kind_icons.Array = ""
lvim.builtin.cmp.formatting.kind_icons.Boolean = ""
lvim.builtin.cmp.formatting.kind_icons.Class = ""
lvim.builtin.cmp.formatting.kind_icons.Color = ""
lvim.builtin.cmp.formatting.kind_icons.Constant = ""
lvim.builtin.cmp.formatting.kind_icons.Constructor = ""
lvim.builtin.cmp.formatting.kind_icons.Enum = ""
lvim.builtin.cmp.formatting.kind_icons.EnumMember = ""
lvim.builtin.cmp.formatting.kind_icons.Event = ""
lvim.builtin.cmp.formatting.kind_icons.Field = ""
lvim.builtin.cmp.formatting.kind_icons.File = ""
lvim.builtin.cmp.formatting.kind_icons.Folder = "󰉋"
lvim.builtin.cmp.formatting.kind_icons.Function = ""
lvim.builtin.cmp.formatting.kind_icons.Interface = ""
lvim.builtin.cmp.formatting.kind_icons.Key = ""
lvim.builtin.cmp.formatting.kind_icons.Keyword = ""
lvim.builtin.cmp.formatting.kind_icons.Method = ""
lvim.builtin.cmp.formatting.kind_icons.Module = ""
lvim.builtin.cmp.formatting.kind_icons.Namespace = ""
lvim.builtin.cmp.formatting.kind_icons.Null = "󰟢"
lvim.builtin.cmp.formatting.kind_icons.Number = ""
lvim.builtin.cmp.formatting.kind_icons.Object = ""
lvim.builtin.cmp.formatting.kind_icons.Operator = ""
lvim.builtin.cmp.formatting.kind_icons.Package = ""
lvim.builtin.cmp.formatting.kind_icons.Property = ""
lvim.builtin.cmp.formatting.kind_icons.Reference = ""
lvim.builtin.cmp.formatting.kind_icons.Snippet = ""
lvim.builtin.cmp.formatting.kind_icons.String = ""
lvim.builtin.cmp.formatting.kind_icons.Struct = ""
lvim.builtin.cmp.formatting.kind_icons.Text = ""
lvim.builtin.cmp.formatting.kind_icons.TypeParameter = ""
lvim.builtin.cmp.formatting.kind_icons.Unit = ""
lvim.builtin.cmp.formatting.kind_icons.Value = ""
lvim.builtin.cmp.formatting.kind_icons.Variable = ""
lvim.builtin.cmp.formatting.max_width = 0
lvim.builtin.cmp.formatting.source_names.buffer = "(Buffer)"
lvim.builtin.cmp.formatting.source_names.calc = "(Calc)"
lvim.builtin.cmp.formatting.source_names.cmp_tabnine = "(Tabnine)"
lvim.builtin.cmp.formatting.source_names.copilot = "(Copilot)"
lvim.builtin.cmp.formatting.source_names.emoji = "(Emoji)"
lvim.builtin.cmp.formatting.source_names.luasnip = "(Snippet)"
lvim.builtin.cmp.formatting.source_names.nvim_lsp = "(LSP)"
lvim.builtin.cmp.formatting.source_names.path = "(Path)"
lvim.builtin.cmp.formatting.source_names.tmux = "(TMUX)"
lvim.builtin.cmp.formatting.source_names.treesitter = "(TreeSitter)"
lvim.builtin.cmp.formatting.source_names.vsnip = "(Snippet)"
-- lvim.builtin.cmp.mapping["<C-D>"] = function ()
-- lvim.builtin.cmp.mapping["<C-E>"] = function ()
-- lvim.builtin.cmp.mapping["<C-F>"] = function ()
-- lvim.builtin.cmp.mapping["<C-J>"].c = function ()
-- lvim.builtin.cmp.mapping["<C-J>"].i = function ()
-- lvim.builtin.cmp.mapping["<C-K>"].c = function ()
-- lvim.builtin.cmp.mapping["<C-K>"].i = function ()
-- lvim.builtin.cmp.mapping["<C-N>"].i = function ()
-- lvim.builtin.cmp.mapping["<C-P>"].i = function ()
-- lvim.builtin.cmp.mapping["<CR>"].i = function ()
-- lvim.builtin.cmp.mapping["<C-Space>"] = function ()
-- lvim.builtin.cmp.mapping["<C-Y>"].c = function ()
-- lvim.builtin.cmp.mapping["<C-Y>"].i = function ()
-- lvim.builtin.cmp.mapping["<Down>"].i = function ()
-- lvim.builtin.cmp.mapping["<S-Tab>"].i = function ()
-- lvim.builtin.cmp.mapping["<S-Tab>"].s = function ()
-- lvim.builtin.cmp.mapping["<Tab>"].i = function ()
-- lvim.builtin.cmp.mapping["<Tab>"].s = function ()
-- lvim.builtin.cmp.mapping["<Up>"].i = function ()
-- lvim.builtin.cmp.snippet.expand = function ()
lvim.builtin.cmp.sources["10"].name = "treesitter"
lvim.builtin.cmp.sources["11"].name = "crates"
lvim.builtin.cmp.sources["12"].name = "tmux"
lvim.builtin.cmp.sources["1"].max_item_count = 3
lvim.builtin.cmp.sources["1"].name = "copilot"
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["1"] = "."
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["10"] = "@"
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["11"] = "|"
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["12"] = "="
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["13"] = "-"
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["14"] = "{"
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["15"] = "/"
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["16"] = "\\"
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["17"] = "+"
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["18"] = "?"
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["19"] = " "
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["2"] = ":"
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["3"] = "("
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["4"] = "'"
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["5"] = "\""
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["6"] = "["
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["7"] = ","
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["8"] = "#"
lvim.builtin.cmp.sources["1"].trigger_characters["1"]["9"] = "*"
-- lvim.builtin.cmp.sources["2"].entry_filter = function ()
lvim.builtin.cmp.sources["2"].name = "nvim_lsp"
lvim.builtin.cmp.sources["3"].name = "path"
lvim.builtin.cmp.sources["4"].name = "luasnip"
lvim.builtin.cmp.sources["5"].name = "cmp_tabnine"
lvim.builtin.cmp.sources["6"].name = "nvim_lua"
lvim.builtin.cmp.sources["7"].name = "buffer"
lvim.builtin.cmp.sources["8"].name = "calc"
lvim.builtin.cmp.sources["9"].name = "emoji"
lvim.builtin.cmp.window.completion.border = "rounded"
lvim.builtin.cmp.window.completion.col_offset = 0
lvim.builtin.cmp.window.completion.scrollbar = true
lvim.builtin.cmp.window.completion.scrolloff = 0
lvim.builtin.cmp.window.completion.side_padding = 1
lvim.builtin.cmp.window.completion.winhighlight = "Normal:Normal,FloatBorder:FloatBorder,CursorLine:Visual,Search:None"
lvim.builtin.cmp.window.completion.zindex = 1001
lvim.builtin.cmp.window.documentation.border = "rounded"
lvim.builtin.cmp.window.documentation.col_offset = 0
lvim.builtin.cmp.window.documentation.scrollbar = true
lvim.builtin.cmp.window.documentation.scrolloff = 0
lvim.builtin.cmp.window.documentation.side_padding = 1
lvim.builtin.cmp.window.documentation.winhighlight = "Normal:Normal,FloatBorder:FloatBorder,CursorLine:Visual,Search:None"
lvim.builtin.cmp.window.documentation.zindex = 1001
lvim.builtin.comment.active = true
lvim.builtin.comment.extra.above = "gcO"
lvim.builtin.comment.extra.below = "gco"
lvim.builtin.comment.extra.eol = "gcA"
lvim.builtin.comment.ignore = "^$"
lvim.builtin.comment.mappings.basic = true
lvim.builtin.comment.mappings.extra = true
lvim.builtin.comment.opleader.block = "gb"
lvim.builtin.comment.opleader.line = "gc"
lvim.builtin.comment.padding = true
-- lvim.builtin.comment.pre_hook = function ()
lvim.builtin.comment.sticky = true
lvim.builtin.comment.toggler.block = "gbc"
lvim.builtin.comment.toggler.line = "gcc"
lvim.builtin.dap.active = true
lvim.builtin.dap.breakpoint.linehl = ""
lvim.builtin.dap.breakpoint.numhl = ""
lvim.builtin.dap.breakpoint_rejected.linehl = ""
lvim.builtin.dap.breakpoint_rejected.numhl = ""
lvim.builtin.dap.breakpoint_rejected.text = ""
lvim.builtin.dap.breakpoint_rejected.texthl = "DiagnosticSignError"
lvim.builtin.dap.breakpoint.text = ""
lvim.builtin.dap.breakpoint.texthl = "DiagnosticSignError"
lvim.builtin.dap.log.level = "info"
lvim.builtin.dap.stopped.linehl = "Visual"
lvim.builtin.dap.stopped.numhl = "DiagnosticSignWarn"
lvim.builtin.dap.stopped.text = ""
lvim.builtin.dap.stopped.texthl = "DiagnosticSignWarn"
lvim.builtin.dap.ui.auto_open = true
lvim.builtin.dap.ui.config.controls.element = "repl"
lvim.builtin.dap.ui.config.controls.enabled = true
lvim.builtin.dap.ui.config.controls.icons.pause = ""
lvim.builtin.dap.ui.config.controls.icons.play = ""
lvim.builtin.dap.ui.config.controls.icons.run_last = ""
lvim.builtin.dap.ui.config.controls.icons.step_back = ""
lvim.builtin.dap.ui.config.controls.icons.step_into = ""
lvim.builtin.dap.ui.config.controls.icons.step_out = ""
lvim.builtin.dap.ui.config.controls.icons.step_over = ""
lvim.builtin.dap.ui.config.controls.icons.terminate = ""
lvim.builtin.dap.ui.config.expand_lines = true
lvim.builtin.dap.ui.config.floating.border = "rounded"
lvim.builtin.dap.ui.config.floating.mappings.close["1"] = "q"
lvim.builtin.dap.ui.config.floating.mappings.close["2"] = "<Esc>"
lvim.builtin.dap.ui.config.floating.max_height = 0.9
lvim.builtin.dap.ui.config.floating.max_width = 0.5
lvim.builtin.dap.ui.config.icons.circular = ""
lvim.builtin.dap.ui.config.icons.collapsed = ""
lvim.builtin.dap.ui.config.icons.expanded = ""
lvim.builtin.dap.ui.config.layouts["1"].elements["1"].id = "scopes"
lvim.builtin.dap.ui.config.layouts["1"].elements["1"].size = 0.33
lvim.builtin.dap.ui.config.layouts["1"].elements["2"].id = "breakpoints"
lvim.builtin.dap.ui.config.layouts["1"].elements["2"].size = 0.17
lvim.builtin.dap.ui.config.layouts["1"].elements["3"].id = "stacks"
lvim.builtin.dap.ui.config.layouts["1"].elements["3"].size = 0.25
lvim.builtin.dap.ui.config.layouts["1"].elements["4"].id = "watches"
lvim.builtin.dap.ui.config.layouts["1"].elements["4"].size = 0.25
lvim.builtin.dap.ui.config.layouts["1"].position = "right"
lvim.builtin.dap.ui.config.layouts["1"].size = 0.33
lvim.builtin.dap.ui.config.layouts["2"].elements["1"].id = "repl"
lvim.builtin.dap.ui.config.layouts["2"].elements["1"].size = 0.45
lvim.builtin.dap.ui.config.layouts["2"].elements["2"].id = "console"
lvim.builtin.dap.ui.config.layouts["2"].elements["2"].size = 0.55
lvim.builtin.dap.ui.config.layouts["2"].position = "bottom"
lvim.builtin.dap.ui.config.layouts["2"].size = 0.27
lvim.builtin.dap.ui.config.mappings.edit = "e"
lvim.builtin.dap.ui.config.mappings.expand["1"] = "<CR>"
lvim.builtin.dap.ui.config.mappings.expand["2"] = "<2-LeftMouse>"
lvim.builtin.dap.ui.config.mappings.open = "o"
lvim.builtin.dap.ui.config.mappings.remove = "d"
lvim.builtin.dap.ui.config.mappings.repl = "r"
lvim.builtin.dap.ui.config.mappings.toggle = "t"
lvim.builtin.dap.ui.config.render.max_value_lines = 100
lvim.builtin.dap.ui.config.windows.indent = 1
lvim.builtin.dap.ui.notify.threshold = 2
lvim.builtin.gitsigns.active = true
lvim.builtin.gitsigns.opts.attach_to_untracked = true
lvim.builtin.gitsigns.opts.current_line_blame = false
lvim.builtin.gitsigns.opts.current_line_blame_formatter = "<author>, <author_time:%Y-%m-%d> - <summary>"
lvim.builtin.gitsigns.opts.current_line_blame_opts.delay = 1000
lvim.builtin.gitsigns.opts.current_line_blame_opts.ignore_whitespace = false
lvim.builtin.gitsigns.opts.current_line_blame_opts.virt_text_pos = "eol"
lvim.builtin.gitsigns.opts.current_line_blame_opts.virt_text = true
lvim.builtin.gitsigns.opts.linehl = false
lvim.builtin.gitsigns.opts.max_file_length = 40000
lvim.builtin.gitsigns.opts.numhl = false
lvim.builtin.gitsigns.opts.preview_config.border = "rounded"
lvim.builtin.gitsigns.opts.preview_config.col = 1
lvim.builtin.gitsigns.opts.preview_config.relative = "cursor"
lvim.builtin.gitsigns.opts.preview_config.row = 0
lvim.builtin.gitsigns.opts.preview_config.style = "minimal"
lvim.builtin.gitsigns.opts.signcolumn = true
lvim.builtin.gitsigns.opts.sign_priority = 6
lvim.builtin.gitsigns.opts.signs.add.hl = "GitSignsAdd"
lvim.builtin.gitsigns.opts.signs.add.linehl = "GitSignsAddLn"
lvim.builtin.gitsigns.opts.signs.add.numhl = "GitSignsAddNr"
lvim.builtin.gitsigns.opts.signs.add.text = "▎"
lvim.builtin.gitsigns.opts.signs.changedelete.hl = "GitSignsChange"
lvim.builtin.gitsigns.opts.signs.changedelete.linehl = "GitSignsChangeLn"
lvim.builtin.gitsigns.opts.signs.changedelete.numhl = "GitSignsChangeNr"
lvim.builtin.gitsigns.opts.signs.changedelete.text = "▎"
lvim.builtin.gitsigns.opts.signs.change.hl = "GitSignsChange"
lvim.builtin.gitsigns.opts.signs.change.linehl = "GitSignsChangeLn"
lvim.builtin.gitsigns.opts.signs.change.numhl = "GitSignsChangeNr"
lvim.builtin.gitsigns.opts.signs.change.text = "▎"
lvim.builtin.gitsigns.opts.signs.delete.hl = "GitSignsDelete"
lvim.builtin.gitsigns.opts.signs.delete.linehl = "GitSignsDeleteLn"
lvim.builtin.gitsigns.opts.signs.delete.numhl = "GitSignsDeleteNr"
lvim.builtin.gitsigns.opts.signs.delete.text = "󰐊"
lvim.builtin.gitsigns.opts.signs.topdelete.hl = "GitSignsDelete"
lvim.builtin.gitsigns.opts.signs.topdelete.linehl = "GitSignsDeleteLn"
lvim.builtin.gitsigns.opts.signs.topdelete.numhl = "GitSignsDeleteNr"
lvim.builtin.gitsigns.opts.signs.topdelete.text = "󰐊"
lvim.builtin.gitsigns.opts.update_debounce = 200
lvim.builtin.gitsigns.opts.watch_gitdir.follow_files = true
lvim.builtin.gitsigns.opts.watch_gitdir.interval = 1000
lvim.builtin.gitsigns.opts.word_diff = false
lvim.builtin.gitsigns.opts.yadm.enable = false
lvim.builtin.illuminate.active = true
lvim.builtin.illuminate.options.delay = 120
lvim.builtin.illuminate.options.filetypes_denylist["10"] = "spectre_panel"
lvim.builtin.illuminate.options.filetypes_denylist["11"] = "toggleterm"
lvim.builtin.illuminate.options.filetypes_denylist["12"] = "DressingSelect"
lvim.builtin.illuminate.options.filetypes_denylist["13"] = "TelescopePrompt"
lvim.builtin.illuminate.options.filetypes_denylist["1"] = "dirvish"
lvim.builtin.illuminate.options.filetypes_denylist["2"] = "fugitive"
lvim.builtin.illuminate.options.filetypes_denylist["3"] = "alpha"
lvim.builtin.illuminate.options.filetypes_denylist["4"] = "NvimTree"
lvim.builtin.illuminate.options.filetypes_denylist["5"] = "lazy"
lvim.builtin.illuminate.options.filetypes_denylist["6"] = "neogitstatus"
lvim.builtin.illuminate.options.filetypes_denylist["7"] = "Trouble"
lvim.builtin.illuminate.options.filetypes_denylist["8"] = "lir"
lvim.builtin.illuminate.options.filetypes_denylist["9"] = "Outline"
lvim.builtin.illuminate.options.providers["1"] = "lsp"
lvim.builtin.illuminate.options.providers["2"] = "treesitter"
lvim.builtin.illuminate.options.providers["3"] = "regex"
lvim.builtin.illuminate.options.under_cursor = true
lvim.builtin.indentlines.active = true
lvim.builtin.indentlines.options.buftype_exclude["1"] = "terminal"
lvim.builtin.indentlines.options.buftype_exclude["2"] = "nofile"
lvim.builtin.indentlines.options.char = "▏"
lvim.builtin.indentlines.options.context_char = "▏"
lvim.builtin.indentlines.options.enabled = true
lvim.builtin.indentlines.options.filetype_exclude["1"] = "help"
lvim.builtin.indentlines.options.filetype_exclude["2"] = "startify"
lvim.builtin.indentlines.options.filetype_exclude["3"] = "dashboard"
lvim.builtin.indentlines.options.filetype_exclude["4"] = "lazy"
lvim.builtin.indentlines.options.filetype_exclude["5"] = "neogitstatus"
lvim.builtin.indentlines.options.filetype_exclude["6"] = "NvimTree"
lvim.builtin.indentlines.options.filetype_exclude["7"] = "Trouble"
lvim.builtin.indentlines.options.filetype_exclude["8"] = "text"
lvim.builtin.indentlines.options.show_current_context = true
lvim.builtin.indentlines.options.show_first_indent_level = true
lvim.builtin.indentlines.options.show_trailing_blankline_indent = false
lvim.builtin.indentlines.options.use_treesitter = true
lvim.builtin.lir.active = true
lvim.builtin.lir.devicons.enable = true
lvim.builtin.lir.devicons.highlight_dirname = true
lvim.builtin.lir.float.curdir_window.enable = false
lvim.builtin.lir.float.curdir_window.highlight_dirname = true
lvim.builtin.lir.float.winblend = 0
-- lvim.builtin.lir.float.win_opts = function ()
lvim.builtin.lir.hide_cursor = false
lvim.builtin.lir.icon = ""
-- lvim.builtin.lir.mappings.a = function ()
-- lvim.builtin.lir.mappings.A = function ()
-- lvim.builtin.lir.mappings.c = function ()
-- lvim.builtin.lir.mappings["<CR>"] = function ()
-- lvim.builtin.lir.mappings["<C-s>"] = function ()
-- lvim.builtin.lir.mappings["<C-t>"] = function ()
-- lvim.builtin.lir.mappings.d = function ()
-- lvim.builtin.lir.mappings["@"] = function ()
-- lvim.builtin.lir.mappings.h = function ()
-- lvim.builtin.lir.mappings.i = function ()
-- lvim.builtin.lir.mappings.J = function ()
-- lvim.builtin.lir.mappings.l = function ()
-- lvim.builtin.lir.mappings.p = function ()
-- lvim.builtin.lir.mappings.q = function ()
-- lvim.builtin.lir.mappings.r = function ()
-- lvim.builtin.lir.mappings.v = function ()
-- lvim.builtin.lir.mappings.x = function ()
-- lvim.builtin.lir.mappings.Y = function ()
-- lvim.builtin.lir.on_init = function ()
lvim.builtin.lir.show_hidden_files = false
lvim.builtin.lualine.active = true
lvim.builtin.lualine.options.disabled_filetypes.statusline["1"] = "alpha"
lvim.builtin.lualine.options.globalstatus = true
lvim.builtin.lualine.style = "lvim"
lvim.builtin.luasnip.sources.friendly_snippets = true
lvim.builtin.mason.github.download_url_template = "https://github.com/%s/releases/download/%s/%s"
lvim.builtin.mason.icons.package_installed = "◍"
lvim.builtin.mason.icons.package_pending = "◍"
lvim.builtin.mason.icons.package_uninstalled = "◍"
lvim.builtin.mason.install_root_dir = "/home/<USER>/.local/share/lvim/mason"
lvim.builtin.mason.log_level = 2
lvim.builtin.mason.max_concurrent_installers = 4
lvim.builtin.mason.PATH = "skip"
lvim.builtin.mason.pip.upgrade_pip = false
lvim.builtin.mason.providers["1"] = "mason.providers.registry-api"
lvim.builtin.mason.providers["2"] = "mason.providers.client"
lvim.builtin.mason.registries["1"] = "lua:mason-registry.index"
lvim.builtin.mason.registries["2"] = "github:mason-org/mason-registry"
lvim.builtin.mason.ui.border = "rounded"
lvim.builtin.mason.ui.check_outdated_packages_on_open = true
lvim.builtin.mason.ui.height = 0.9
lvim.builtin.mason.ui.keymaps.apply_language_filter = "<C-f>"
lvim.builtin.mason.ui.keymaps.cancel_installation = "<C-c>"
lvim.builtin.mason.ui.keymaps.check_outdated_packages = "C"
lvim.builtin.mason.ui.keymaps.check_package_version = "c"
lvim.builtin.mason.ui.keymaps.install_package = "i"
lvim.builtin.mason.ui.keymaps.toggle_package_expand = "<CR>"
lvim.builtin.mason.ui.keymaps.uninstall_package = "X"
lvim.builtin.mason.ui.keymaps.update_all_packages = "U"
lvim.builtin.mason.ui.keymaps.update_package = "u"
lvim.builtin.mason.ui.width = 0.8
lvim.builtin.nvimtree.active = true
lvim.builtin.nvimtree.setup.actions.change_dir.enable = true
lvim.builtin.nvimtree.setup.actions.change_dir.global = false
lvim.builtin.nvimtree.setup.actions.change_dir.restrict_above_cwd = false
lvim.builtin.nvimtree.setup.actions.expand_all.max_folder_discovery = 300
lvim.builtin.nvimtree.setup.actions.file_popup.open_win_config.border = "shadow"
lvim.builtin.nvimtree.setup.actions.file_popup.open_win_config.col = 1
lvim.builtin.nvimtree.setup.actions.file_popup.open_win_config.relative = "cursor"
lvim.builtin.nvimtree.setup.actions.file_popup.open_win_config.row = 1
lvim.builtin.nvimtree.setup.actions.file_popup.open_win_config.style = "minimal"
lvim.builtin.nvimtree.setup.actions.open_file.eject = true
lvim.builtin.nvimtree.setup.actions.open_file.quit_on_open = false
lvim.builtin.nvimtree.setup.actions.open_file.resize_window = false
lvim.builtin.nvimtree.setup.actions.open_file.window_picker.chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890"
lvim.builtin.nvimtree.setup.actions.open_file.window_picker.enable = true
lvim.builtin.nvimtree.setup.actions.open_file.window_picker.exclude.buftype["1"] = "nofile"
lvim.builtin.nvimtree.setup.actions.open_file.window_picker.exclude.buftype["2"] = "terminal"
lvim.builtin.nvimtree.setup.actions.open_file.window_picker.exclude.buftype["3"] = "help"
lvim.builtin.nvimtree.setup.actions.open_file.window_picker.exclude.filetype["1"] = "notify"
lvim.builtin.nvimtree.setup.actions.open_file.window_picker.exclude.filetype["2"] = "lazy"
lvim.builtin.nvimtree.setup.actions.open_file.window_picker.exclude.filetype["3"] = "qf"
lvim.builtin.nvimtree.setup.actions.open_file.window_picker.exclude.filetype["4"] = "diff"
lvim.builtin.nvimtree.setup.actions.open_file.window_picker.exclude.filetype["5"] = "fugitive"
lvim.builtin.nvimtree.setup.actions.open_file.window_picker.exclude.filetype["6"] = "fugitiveblame"
lvim.builtin.nvimtree.setup.actions.open_file.window_picker.picker = "default"
lvim.builtin.nvimtree.setup.actions.remove_file.close_window = true
lvim.builtin.nvimtree.setup.actions.use_system_clipboard = true
lvim.builtin.nvimtree.setup.auto_reload_on_write = false
lvim.builtin.nvimtree.setup.diagnostics.debounce_delay = 50
lvim.builtin.nvimtree.setup.diagnostics.enable = true
lvim.builtin.nvimtree.setup.diagnostics.icons.error = ""
lvim.builtin.nvimtree.setup.diagnostics.icons.hint = ""
lvim.builtin.nvimtree.setup.diagnostics.icons.info = ""
lvim.builtin.nvimtree.setup.diagnostics.icons.warning = ""
lvim.builtin.nvimtree.setup.diagnostics.severity.max = 1
lvim.builtin.nvimtree.setup.diagnostics.severity.min = 4
lvim.builtin.nvimtree.setup.diagnostics.show_on_dirs = false
lvim.builtin.nvimtree.setup.diagnostics.show_on_open_dirs = true
lvim.builtin.nvimtree.setup.disable_netrw = false
lvim.builtin.nvimtree.setup.filesystem_watchers.debounce_delay = 50
lvim.builtin.nvimtree.setup.filesystem_watchers.enable = true
lvim.builtin.nvimtree.setup.filters.custom["1"] = "node_modules"
lvim.builtin.nvimtree.setup.filters.custom["2"] = "\\.cache"
lvim.builtin.nvimtree.setup.filters.dotfiles = false
lvim.builtin.nvimtree.setup.filters.enable = true
lvim.builtin.nvimtree.setup.filters.git_clean = false
lvim.builtin.nvimtree.setup.filters.git_ignored = false
lvim.builtin.nvimtree.setup.filters.no_bookmark = false
lvim.builtin.nvimtree.setup.filters.no_buffer = false
lvim.builtin.nvimtree.setup.git.cygwin_support = false
lvim.builtin.nvimtree.setup.git.enable = true
lvim.builtin.nvimtree.setup.git.show_on_dirs = true
lvim.builtin.nvimtree.setup.git.show_on_open_dirs = true
lvim.builtin.nvimtree.setup.git.timeout = 400
lvim.builtin.nvimtree.setup.help.sort_by = "key"
lvim.builtin.nvimtree.setup.hijack_cursor = false
lvim.builtin.nvimtree.setup.hijack_directories.auto_open = true
lvim.builtin.nvimtree.setup.hijack_directories.enable = false
lvim.builtin.nvimtree.setup.hijack_netrw = true
lvim.builtin.nvimtree.setup.hijack_unnamed_buffer_when_opening = false
lvim.builtin.nvimtree.setup.live_filter.always_show_folders = true
lvim.builtin.nvimtree.setup.live_filter.prefix = "[FILTER]: "
lvim.builtin.nvimtree.setup.log.enable = false
lvim.builtin.nvimtree.setup.log.truncate = false
lvim.builtin.nvimtree.setup.log.types.all = false
lvim.builtin.nvimtree.setup.log.types.config = false
lvim.builtin.nvimtree.setup.log.types.copy_paste = false
lvim.builtin.nvimtree.setup.log.types.dev = false
lvim.builtin.nvimtree.setup.log.types.diagnostics = false
lvim.builtin.nvimtree.setup.log.types.git = false
lvim.builtin.nvimtree.setup.log.types.profile = false
lvim.builtin.nvimtree.setup.log.types.watcher = false
lvim.builtin.nvimtree.setup.modified.enable = false
lvim.builtin.nvimtree.setup.modified.show_on_dirs = true
lvim.builtin.nvimtree.setup.modified.show_on_open_dirs = true
lvim.builtin.nvimtree.setup.notify.absolute_path = true
lvim.builtin.nvimtree.setup.notify.threshold = 2
lvim.builtin.nvimtree.setup.on_attach = "default"
lvim.builtin.nvimtree.setup.prefer_startup_root = false
lvim.builtin.nvimtree.setup.reload_on_bufenter = false
lvim.builtin.nvimtree.setup.renderer.add_trailing = false
lvim.builtin.nvimtree.setup.renderer.full_name = false
lvim.builtin.nvimtree.setup.renderer.group_empty = false
lvim.builtin.nvimtree.setup.renderer.highlight_bookmarks = "none"
lvim.builtin.nvimtree.setup.renderer.highlight_clipboard = "name"
lvim.builtin.nvimtree.setup.renderer.highlight_diagnostics = "none"
lvim.builtin.nvimtree.setup.renderer.highlight_git = "name"
lvim.builtin.nvimtree.setup.renderer.highlight_modified = "none"
lvim.builtin.nvimtree.setup.renderer.highlight_opened_files = "none"
lvim.builtin.nvimtree.setup.renderer.icons.bookmarks_placement = "signcolumn"
lvim.builtin.nvimtree.setup.renderer.icons.diagnostics_placement = "signcolumn"
lvim.builtin.nvimtree.setup.renderer.icons.git_placement = "before"
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.bookmark = ""
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.default = ""
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.folder.arrow_closed = ""
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.folder.arrow_open = ""
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.folder.default = "󰉋"
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.folder.empty = ""
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.folder.empty_open = ""
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.folder.open = ""
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.folder.symlink = ""
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.folder.symlink_open = ""
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.git.deleted = ""
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.git.ignored = "◌"
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.git.renamed = ""
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.git.staged = "S"
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.git.unmerged = ""
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.git.unstaged = ""
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.git.untracked = "U"
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.modified = " "
lvim.builtin.nvimtree.setup.renderer.icons.glyphs.symlink = ""
lvim.builtin.nvimtree.setup.renderer.icons.modified_placement = "after"
lvim.builtin.nvimtree.setup.renderer.icons.padding = " "
lvim.builtin.nvimtree.setup.renderer.icons.show.bookmarks = true
lvim.builtin.nvimtree.setup.renderer.icons.show.diagnostics = true
lvim.builtin.nvimtree.setup.renderer.icons.show.file = true
lvim.builtin.nvimtree.setup.renderer.icons.show.folder_arrow = true
lvim.builtin.nvimtree.setup.renderer.icons.show.folder = true
lvim.builtin.nvimtree.setup.renderer.icons.show.git = true
lvim.builtin.nvimtree.setup.renderer.icons.show.modified = true
lvim.builtin.nvimtree.setup.renderer.icons.symlink_arrow = " ➛ "
lvim.builtin.nvimtree.setup.renderer.icons.webdev_colors = true
lvim.builtin.nvimtree.setup.renderer.icons.web_devicons.file.color = true
lvim.builtin.nvimtree.setup.renderer.icons.web_devicons.file.enable = true
lvim.builtin.nvimtree.setup.renderer.icons.web_devicons.folder.color = true
lvim.builtin.nvimtree.setup.renderer.icons.web_devicons.folder.enable = false
lvim.builtin.nvimtree.setup.renderer.indent_markers.enable = false
lvim.builtin.nvimtree.setup.renderer.indent_markers.icons.bottom = "─"
lvim.builtin.nvimtree.setup.renderer.indent_markers.icons.corner = "└"
lvim.builtin.nvimtree.setup.renderer.indent_markers.icons.edge = "│"
lvim.builtin.nvimtree.setup.renderer.indent_markers.icons.item = "│"
lvim.builtin.nvimtree.setup.renderer.indent_markers.icons.none = " "
lvim.builtin.nvimtree.setup.renderer.indent_markers.inline_arrows = true
lvim.builtin.nvimtree.setup.renderer.indent_width = 2
lvim.builtin.nvimtree.setup.renderer.root_folder_label = ":t"
lvim.builtin.nvimtree.setup.renderer.special_files["1"] = "Cargo.toml"
lvim.builtin.nvimtree.setup.renderer.special_files["2"] = "Makefile"
lvim.builtin.nvimtree.setup.renderer.special_files["3"] = "README.md"
lvim.builtin.nvimtree.setup.renderer.special_files["4"] = "readme.md"
lvim.builtin.nvimtree.setup.renderer.symlink_destination = true
lvim.builtin.nvimtree.setup.respect_buf_cwd = false
lvim.builtin.nvimtree.setup.select_prompts = false
lvim.builtin.nvimtree.setup.sort.files_first = false
lvim.builtin.nvimtree.setup.sort.folders_first = true
lvim.builtin.nvimtree.setup.sort.sorter = "name"
lvim.builtin.nvimtree.setup.sync_root_with_cwd = true
lvim.builtin.nvimtree.setup.tab.sync.close = false
lvim.builtin.nvimtree.setup.tab.sync.open = false
lvim.builtin.nvimtree.setup.trash.cmd = "gio trash"
lvim.builtin.nvimtree.setup.ui.confirm.default_yes = false
lvim.builtin.nvimtree.setup.ui.confirm.remove = true
lvim.builtin.nvimtree.setup.ui.confirm.trash = true
lvim.builtin.nvimtree.setup.update_focused_file.enable = true
lvim.builtin.nvimtree.setup.update_focused_file.exclude = false
lvim.builtin.nvimtree.setup.update_focused_file.update_root.enable = true
lvim.builtin.nvimtree.setup.view.adaptive_size = false
lvim.builtin.nvimtree.setup.view.centralize_selection = true
lvim.builtin.nvimtree.setup.view.cursorline = true
lvim.builtin.nvimtree.setup.view.debounce_delay = 15
lvim.builtin.nvimtree.setup.view.float.enable = false
lvim.builtin.nvimtree.setup.view.float.open_win_config.border = "rounded"
lvim.builtin.nvimtree.setup.view.float.open_win_config.col = 1
lvim.builtin.nvimtree.setup.view.float.open_win_config.height = 30
lvim.builtin.nvimtree.setup.view.float.open_win_config.relative = "editor"
lvim.builtin.nvimtree.setup.view.float.open_win_config.row = 1
lvim.builtin.nvimtree.setup.view.float.open_win_config.width = 30
lvim.builtin.nvimtree.setup.view.float.quit_on_focus_loss = true
lvim.builtin.nvimtree.setup.view.number = false
lvim.builtin.nvimtree.setup.view.preserve_window_proportions = false
lvim.builtin.nvimtree.setup.view.relativenumber = false
lvim.builtin.nvimtree.setup.view.side = "left"
lvim.builtin.nvimtree.setup.view.signcolumn = "yes"
lvim.builtin.nvimtree.setup.view.width = 30
lvim.builtin.project.active = true
lvim.builtin.project.datapath = "/home/<USER>/.cache/lvim"
lvim.builtin.project.detection_methods["1"] = "pattern"
lvim.builtin.project.manual_mode = false
lvim.builtin.project.patterns["1"] = ".git"
lvim.builtin.project.patterns["2"] = "_darcs"
lvim.builtin.project.patterns["3"] = ".hg"
lvim.builtin.project.patterns["4"] = ".bzr"
lvim.builtin.project.patterns["5"] = ".svn"
lvim.builtin.project.patterns["6"] = "Makefile"
lvim.builtin.project.patterns["7"] = "package.json"
lvim.builtin.project.patterns["8"] = "pom.xml"
lvim.builtin.project.scope_chdir = "global"
lvim.builtin.project.show_hidden = false
lvim.builtin.project.silent_chdir = true
lvim.builtin.telescope.active = true
lvim.builtin.telescope.defaults.color_devicons = true
lvim.builtin.telescope.defaults.entry_prefix = "  "
lvim.builtin.telescope.defaults.initial_mode = "insert"
-- lvim.builtin.telescope.defaults.mappings.i["<C-c>"] = function ()
-- lvim.builtin.telescope.defaults.mappings.i["<C-j>"] = function ()
-- lvim.builtin.telescope.defaults.mappings.i["<C-k>"] = function ()
-- lvim.builtin.telescope.defaults.mappings.i["<C-n>"] = function ()
-- lvim.builtin.telescope.defaults.mappings.i["<C-p>"] = function ()
-- lvim.builtin.telescope.defaults.mappings.i["<C-q>"] = function ()
-- lvim.builtin.telescope.defaults.mappings.i["<CR>"] = function ()
-- lvim.builtin.telescope.defaults.mappings.n["<C-n>"] = function ()
-- lvim.builtin.telescope.defaults.mappings.n["<C-p>"] = function ()
-- lvim.builtin.telescope.defaults.mappings.n["<C-q>"] = function ()
lvim.builtin.telescope.defaults.path_display["1"] = "smart"
lvim.builtin.telescope.defaults.prompt_prefix = " "
lvim.builtin.telescope.defaults.selection_caret = " "
lvim.builtin.telescope.defaults.selection_strategy = "reset"
lvim.builtin.telescope.defaults.set_env.COLORTERM = "truecolor"
lvim.builtin.telescope.defaults.vimgrep_arguments["1"] = "rg"
lvim.builtin.telescope.defaults.vimgrep_arguments["2"] = "--color=never"
lvim.builtin.telescope.defaults.vimgrep_arguments["3"] = "--no-heading"
lvim.builtin.telescope.defaults.vimgrep_arguments["4"] = "--with-filename"
lvim.builtin.telescope.defaults.vimgrep_arguments["5"] = "--line-number"
lvim.builtin.telescope.defaults.vimgrep_arguments["6"] = "--column"
lvim.builtin.telescope.defaults.vimgrep_arguments["7"] = "--smart-case"
lvim.builtin.telescope.defaults.vimgrep_arguments["8"] = "--hidden"
lvim.builtin.telescope.defaults.vimgrep_arguments["9"] = "--glob=!.git/"
lvim.builtin.telescope.defaults.winblend = 0
lvim.builtin.telescope.extensions.fzf.case_mode = "smart_case"
lvim.builtin.telescope.extensions.fzf.fuzzy = true
lvim.builtin.telescope.extensions.fzf.override_file_sorter = true
lvim.builtin.telescope.extensions.fzf.override_generic_sorter = true
lvim.builtin.telescope.pickers.buffers.initial_mode = "normal"
-- lvim.builtin.telescope.pickers.buffers.mappings.i["<C-d>"] = function ()
-- lvim.builtin.telescope.pickers.buffers.mappings.n.dd = function ()
lvim.builtin.telescope.pickers.colorscheme.enable_preview = true
lvim.builtin.telescope.pickers.find_files.hidden = true
lvim.builtin.telescope.pickers.git_files.hidden = true
lvim.builtin.telescope.pickers.git_files.show_untracked = true
lvim.builtin.telescope.pickers.grep_string.only_sort_text = true
lvim.builtin.telescope.pickers.live_grep.only_sort_text = true
lvim.builtin.telescope.pickers.planets.show_moon = true
lvim.builtin.telescope.pickers.planets.show_pluto = true
lvim.builtin.telescope.theme = "dropdown"
lvim.builtin.terminal.active = true
lvim.builtin.terminal.auto_scroll = true
lvim.builtin.terminal.close_on_exit = true
lvim.builtin.terminal.direction = "float"
lvim.builtin.terminal.execs["1"]["2"] = "<M-1>"
lvim.builtin.terminal.execs["1"]["3"] = "Horizontal Terminal"
lvim.builtin.terminal.execs["1"]["4"] = "horizontal"
lvim.builtin.terminal.execs["1"]["5"] = 0.3
lvim.builtin.terminal.execs["2"]["2"] = "<M-2>"
lvim.builtin.terminal.execs["2"]["3"] = "Vertical Terminal"
lvim.builtin.terminal.execs["2"]["4"] = "vertical"
lvim.builtin.terminal.execs["2"]["5"] = 0.4
lvim.builtin.terminal.execs["3"]["2"] = "<M-3>"
lvim.builtin.terminal.execs["3"]["3"] = "Float Terminal"
lvim.builtin.terminal.execs["3"]["4"] = "float"
lvim.builtin.terminal.float_opts.border = "curved"
lvim.builtin.terminal.float_opts.highlights.background = "Normal"
lvim.builtin.terminal.float_opts.highlights.border = "Normal"
lvim.builtin.terminal.float_opts.winblend = 0
lvim.builtin.terminal.hide_numbers = true
lvim.builtin.terminal.insert_mappings = true
lvim.builtin.terminal.open_mapping = "<c-\\>"
lvim.builtin.terminal.persist_size = false
lvim.builtin.terminal.shade_terminals = true
lvim.builtin.terminal.shading_factor = 2
lvim.builtin.terminal.size = 20
lvim.builtin.terminal.start_in_insert = true
lvim.builtin.terminal.winbar.enabled = false
lvim.builtin.theme.name = "lunar"
lvim.builtin.theme.tokyonight.options.day_brightness = 0.3
lvim.builtin.theme.tokyonight.options.dim_inactive = false
lvim.builtin.theme.tokyonight.options.hide_inactive_statusline = false
lvim.builtin.theme.tokyonight.options.lualine_bold = false
-- lvim.builtin.theme.tokyonight.options.on_highlights = function ()
lvim.builtin.theme.tokyonight.options.sidebars["1"] = "qf"
lvim.builtin.theme.tokyonight.options.sidebars["2"] = "vista_kind"
lvim.builtin.theme.tokyonight.options.sidebars["3"] = "terminal"
lvim.builtin.theme.tokyonight.options.sidebars["4"] = "packer"
lvim.builtin.theme.tokyonight.options.sidebars["5"] = "spectre_panel"
lvim.builtin.theme.tokyonight.options.sidebars["6"] = "NeogitStatus"
lvim.builtin.theme.tokyonight.options.sidebars["7"] = "help"
lvim.builtin.theme.tokyonight.options.style = "night"
lvim.builtin.theme.tokyonight.options.styles.comments.italic = true
lvim.builtin.theme.tokyonight.options.styles.floats = "dark"
lvim.builtin.theme.tokyonight.options.styles.keywords.italic = true
lvim.builtin.theme.tokyonight.options.styles.sidebars = "dark"
lvim.builtin.theme.tokyonight.options.terminal_colors = true
lvim.builtin.theme.tokyonight.options.transparent = false
lvim.builtin.theme.tokyonight.options.use_background = true
lvim.builtin.treesitter.auto_install = true
lvim.builtin.treesitter.autotag.enable = false
lvim.builtin.treesitter.context_commentstring.config.css = "/* %s */"
lvim.builtin.treesitter.context_commentstring.config.html = "<!-- %s -->"
lvim.builtin.treesitter.context_commentstring.config.json = ""
lvim.builtin.treesitter.context_commentstring.config.scss = "/* %s */"
lvim.builtin.treesitter.context_commentstring.config.svelte = "<!-- %s -->"
lvim.builtin.treesitter.context_commentstring.config.typescript = "// %s"
lvim.builtin.treesitter.context_commentstring.config.vue = "<!-- %s -->"
lvim.builtin.treesitter.context_commentstring.enable_autocmd = false
lvim.builtin.treesitter.context_commentstring.enable = true
lvim.builtin.treesitter.ensure_installed["1"] = "comment"
lvim.builtin.treesitter.ensure_installed["2"] = "markdown_inline"
lvim.builtin.treesitter.ensure_installed["3"] = "regex"
lvim.builtin.treesitter.highlight.additional_vim_regex_highlighting = false
-- lvim.builtin.treesitter.highlight.disable = function ()
lvim.builtin.treesitter.highlight.enable = true
lvim.builtin.treesitter.indent.disable["1"] = "yaml"
lvim.builtin.treesitter.indent.disable["2"] = "python"
lvim.builtin.treesitter.indent.enable = true
lvim.builtin.treesitter.matchup.enable = false
lvim.builtin.treesitter.playground.enable = false
lvim.builtin.treesitter.playground.keybindings.focus_language = "f"
lvim.builtin.treesitter.playground.keybindings.goto_node = "<cr>"
lvim.builtin.treesitter.playground.keybindings.show_help = "?"
lvim.builtin.treesitter.playground.keybindings.toggle_anonymous_nodes = "a"
lvim.builtin.treesitter.playground.keybindings.toggle_hl_groups = "i"
lvim.builtin.treesitter.playground.keybindings.toggle_injected_languages = "t"
lvim.builtin.treesitter.playground.keybindings.toggle_language_display = "I"
lvim.builtin.treesitter.playground.keybindings.toggle_query_editor = "o"
lvim.builtin.treesitter.playground.keybindings.unfocus_language = "F"
lvim.builtin.treesitter.playground.keybindings.update = "R"
lvim.builtin.treesitter.playground.persist_queries = false
lvim.builtin.treesitter.playground.updatetime = 25
lvim.builtin.treesitter.rainbow.enable = false
lvim.builtin.treesitter.rainbow.extended_mode = true
lvim.builtin.treesitter.rainbow.max_file_lines = 1000
lvim.builtin.treesitter.sync_install = false
lvim.builtin.treesitter.textobjects.select.enable = false
lvim.builtin.treesitter.textobjects.swap.enable = false
lvim.builtin.treesitter.textsubjects.enable = false
lvim.builtin.treesitter.textsubjects.keymaps[";"] = "textsubjects-big"
lvim.builtin.treesitter.textsubjects.keymaps["."] = "textsubjects-smart"
lvim.builtin.which_key.active = true
lvim.builtin.which_key.mappings[";"]["1"] = "<cmd>Alpha<CR>"
lvim.builtin.which_key.mappings["/"]["1"] = "<Plug>(comment_toggle_linewise_current)"
lvim.builtin.which_key.mappings["/"]["2"] = "Comment toggle current line"
lvim.builtin.which_key.mappings[";"]["2"] = "Dashboard"
lvim.builtin.which_key.mappings.b.b["1"] = "<cmd>BufferLineCyclePrev<cr>"
lvim.builtin.which_key.mappings.b.b["2"] = "Previous"
lvim.builtin.which_key.mappings.b.D["1"] = "<cmd>BufferLineSortByDirectory<cr>"
lvim.builtin.which_key.mappings.b.D["2"] = "Sort by directory"
lvim.builtin.which_key.mappings.b.e["1"] = "<cmd>BufferLinePickClose<cr>"
lvim.builtin.which_key.mappings.b.e["2"] = "Pick which buffer to close"
lvim.builtin.which_key.mappings.b.f["1"] = "<cmd>Telescope buffers previewer=false<cr>"
lvim.builtin.which_key.mappings.b.f["2"] = "Find"
lvim.builtin.which_key.mappings.b.h["1"] = "<cmd>BufferLineCloseLeft<cr>"
lvim.builtin.which_key.mappings.b.h["2"] = "Close all to the left"
lvim.builtin.which_key.mappings.b.j["1"] = "<cmd>BufferLinePick<cr>"
lvim.builtin.which_key.mappings.b.j["2"] = "Jump"
lvim.builtin.which_key.mappings.b.l["1"] = "<cmd>BufferLineCloseRight<cr>"
lvim.builtin.which_key.mappings.b.L["1"] = "<cmd>BufferLineSortByExtension<cr>"
lvim.builtin.which_key.mappings.b.l["2"] = "Close all to the right"
lvim.builtin.which_key.mappings.b.L["2"] = "Sort by language"
lvim.builtin.which_key.mappings.b.n["1"] = "<cmd>BufferLineCycleNext<cr>"
lvim.builtin.which_key.mappings.b.n["2"] = "Next"
lvim.builtin.which_key.mappings.b.name = "Buffers"
lvim.builtin.which_key.mappings.b.W["1"] = "<cmd>noautocmd w<cr>"
lvim.builtin.which_key.mappings.b.W["2"] = "Save without formatting (noautocmd)"
lvim.builtin.which_key.mappings.c["1"] = "<cmd>BufferKill<CR>"
lvim.builtin.which_key.mappings.c["2"] = "Close Buffer"
lvim.builtin.which_key.mappings.d.b["1"] = "<cmd>lua require'dap'.step_back()<cr>"
lvim.builtin.which_key.mappings.d.b["2"] = "Step Back"
lvim.builtin.which_key.mappings.d.c["1"] = "<cmd>lua require'dap'.continue()<cr>"
lvim.builtin.which_key.mappings.d.C["1"] = "<cmd>lua require'dap'.run_to_cursor()<cr>"
lvim.builtin.which_key.mappings.d.c["2"] = "Continue"
lvim.builtin.which_key.mappings.d.C["2"] = "Run To Cursor"
lvim.builtin.which_key.mappings.d.d["1"] = "<cmd>lua require'dap'.disconnect()<cr>"
lvim.builtin.which_key.mappings.d.d["2"] = "Disconnect"
lvim.builtin.which_key.mappings.d.g["1"] = "<cmd>lua require'dap'.session()<cr>"
lvim.builtin.which_key.mappings.d.g["2"] = "Get Session"
lvim.builtin.which_key.mappings.d.i["1"] = "<cmd>lua require'dap'.step_into()<cr>"
lvim.builtin.which_key.mappings.d.i["2"] = "Step Into"
lvim.builtin.which_key.mappings.d.name = "Debug"
lvim.builtin.which_key.mappings.d.o["1"] = "<cmd>lua require'dap'.step_over()<cr>"
lvim.builtin.which_key.mappings.d.o["2"] = "Step Over"
lvim.builtin.which_key.mappings.d.p["1"] = "<cmd>lua require'dap'.pause()<cr>"
lvim.builtin.which_key.mappings.d.p["2"] = "Pause"
lvim.builtin.which_key.mappings.d.q["1"] = "<cmd>lua require'dap'.close()<cr>"
lvim.builtin.which_key.mappings.d.q["2"] = "Quit"
lvim.builtin.which_key.mappings.d.r["1"] = "<cmd>lua require'dap'.repl.toggle()<cr>"
lvim.builtin.which_key.mappings.d.r["2"] = "Toggle Repl"
lvim.builtin.which_key.mappings.d.s["1"] = "<cmd>lua require'dap'.continue()<cr>"
lvim.builtin.which_key.mappings.d.s["2"] = "Start"
lvim.builtin.which_key.mappings.d.t["1"] = "<cmd>lua require'dap'.toggle_breakpoint()<cr>"
lvim.builtin.which_key.mappings.d.t["2"] = "Toggle Breakpoint"
lvim.builtin.which_key.mappings.d.u["1"] = "<cmd>lua require'dap'.step_out()<cr>"
lvim.builtin.which_key.mappings.d.U["1"] = "<cmd>lua require'dapui'.toggle({reset = true})<cr>"
lvim.builtin.which_key.mappings.d.u["2"] = "Step Out"
lvim.builtin.which_key.mappings.d.U["2"] = "Toggle UI"
lvim.builtin.which_key.mappings.e["1"] = "<cmd>NvimTreeToggle<CR>"
lvim.builtin.which_key.mappings.e["2"] = "Explorer"
-- lvim.builtin.which_key.mappings.f["1"] = function ()
lvim.builtin.which_key.mappings.f["2"] = "Find File"
lvim.builtin.which_key.mappings.g.b["1"] = "<cmd>Telescope git_branches<cr>"
lvim.builtin.which_key.mappings.g.b["2"] = "Checkout branch"
lvim.builtin.which_key.mappings.g.C["1"] = "<cmd>Telescope git_bcommits<cr>"
lvim.builtin.which_key.mappings.g.c["1"] = "<cmd>Telescope git_commits<cr>"
lvim.builtin.which_key.mappings.g.c["2"] = "Checkout commit"
lvim.builtin.which_key.mappings.g.C["2"] = "Checkout commit(for current file)"
lvim.builtin.which_key.mappings.g.d["1"] = "<cmd>Gitsigns diffthis HEAD<cr>"
lvim.builtin.which_key.mappings.g.d["2"] = "Git Diff"
lvim.builtin.which_key.mappings.g.g["1"] = "<cmd>lua require 'lvim.core.terminal'.lazygit_toggle()<cr>"
lvim.builtin.which_key.mappings.g.g["2"] = "Lazygit"
lvim.builtin.which_key.mappings.g.j["1"] = "<cmd>lua require 'gitsigns'.nav_hunk('next', {navigation_message = false})<cr>"
lvim.builtin.which_key.mappings.g.j["2"] = "Next Hunk"
lvim.builtin.which_key.mappings.g.k["1"] = "<cmd>lua require 'gitsigns'.nav_hunk('prev', {navigation_message = false})<cr>"
lvim.builtin.which_key.mappings.g.k["2"] = "Prev Hunk"
lvim.builtin.which_key.mappings.g.l["1"] = "<cmd>lua require 'gitsigns'.blame_line()<cr>"
lvim.builtin.which_key.mappings.g.L["1"] = "<cmd>lua require 'gitsigns'.blame_line({full=true})<cr>"
lvim.builtin.which_key.mappings.g.l["2"] = "Blame"
lvim.builtin.which_key.mappings.g.L["2"] = "Blame Line (full)"
lvim.builtin.which_key.mappings.g.name = "Git"
lvim.builtin.which_key.mappings.g.o["1"] = "<cmd>Telescope git_status<cr>"
lvim.builtin.which_key.mappings.g.o["2"] = "Open changed file"
lvim.builtin.which_key.mappings.g.p["1"] = "<cmd>lua require 'gitsigns'.preview_hunk()<cr>"
lvim.builtin.which_key.mappings.g.p["2"] = "Preview Hunk"
lvim.builtin.which_key.mappings.g.R["1"] = "<cmd>lua require 'gitsigns'.reset_buffer()<cr>"
lvim.builtin.which_key.mappings.g.r["1"] = "<cmd>lua require 'gitsigns'.reset_hunk()<cr>"
lvim.builtin.which_key.mappings.g.R["2"] = "Reset Buffer"
lvim.builtin.which_key.mappings.g.r["2"] = "Reset Hunk"
lvim.builtin.which_key.mappings.g.s["1"] = "<cmd>lua require 'gitsigns'.stage_hunk()<cr>"
lvim.builtin.which_key.mappings.g.s["2"] = "Stage Hunk"
lvim.builtin.which_key.mappings.g.u["1"] = "<cmd>lua require 'gitsigns'.undo_stage_hunk()<cr>"
lvim.builtin.which_key.mappings.g.u["2"] = "Undo Stage Hunk"
lvim.builtin.which_key.mappings.h["1"] = "<cmd>nohlsearch<CR>"
lvim.builtin.which_key.mappings.h["2"] = "No Highlight"
lvim.builtin.which_key.mappings.l.a["1"] = "<cmd>lua vim.lsp.buf.code_action()<cr>"
lvim.builtin.which_key.mappings.l.a["2"] = "Code Action"
lvim.builtin.which_key.mappings.L.c["1"] = "<cmd>edit /home/<USER>/.config/lvim/config.lua<cr>"
lvim.builtin.which_key.mappings.L.c["2"] = "Edit config.lua"
lvim.builtin.which_key.mappings.L.d["1"] = "<cmd>LvimDocs<cr>"
lvim.builtin.which_key.mappings.l.d["1"] = "<cmd>Telescope diagnostics bufnr=0 theme=get_ivy<cr>"
lvim.builtin.which_key.mappings.l.d["2"] = "Buffer Diagnostics"
lvim.builtin.which_key.mappings.L.d["2"] = "View LunarVim's docs"
lvim.builtin.which_key.mappings.l.e["1"] = "<cmd>Telescope quickfix<cr>"
lvim.builtin.which_key.mappings.l.e["2"] = "Telescope Quickfix"
lvim.builtin.which_key.mappings.L.f["1"] = "<cmd>lua require('lvim.core.telescope.custom-finders').find_lunarvim_files()<cr>"
lvim.builtin.which_key.mappings.l.f["1"] = "<cmd>lua require('lvim.lsp.utils').format()<cr>"
lvim.builtin.which_key.mappings.L.f["2"] = "Find LunarVim files"
lvim.builtin.which_key.mappings.l.f["2"] = "Format"
lvim.builtin.which_key.mappings.L.g["1"] = "<cmd>lua require('lvim.core.telescope.custom-finders').grep_lunarvim_files()<cr>"
lvim.builtin.which_key.mappings.L.g["2"] = "Grep LunarVim files"
lvim.builtin.which_key.mappings.l.i["1"] = "<cmd>LspInfo<cr>"
lvim.builtin.which_key.mappings.L.i["1"] = "<cmd>lua require('lvim.core.info').toggle_popup(vim.bo.filetype)<cr>"
lvim.builtin.which_key.mappings.L.I["1"] = "<cmd>lua require('lvim.core.telescope.custom-finders').view_lunarvim_changelog()<cr>"
lvim.builtin.which_key.mappings.l.I["1"] = "<cmd>Mason<cr>"
lvim.builtin.which_key.mappings.l.i["2"] = "Info"
lvim.builtin.which_key.mappings.l.I["2"] = "Mason Info"
lvim.builtin.which_key.mappings.L.i["2"] = "Toggle LunarVim Info"
lvim.builtin.which_key.mappings.L.I["2"] = "View LunarVim's changelog"
lvim.builtin.which_key.mappings.l.j["1"] = "<cmd>lua vim.diagnostic.goto_next()<cr>"
lvim.builtin.which_key.mappings.l.j["2"] = "Next Diagnostic"
lvim.builtin.which_key.mappings.l.k["1"] = "<cmd>lua vim.diagnostic.goto_prev()<cr>"
lvim.builtin.which_key.mappings.L.k["1"] = "<cmd>Telescope keymaps<cr>"
lvim.builtin.which_key.mappings.l.k["2"] = "Prev Diagnostic"
lvim.builtin.which_key.mappings.L.k["2"] = "View LunarVim's keymappings"
lvim.builtin.which_key.mappings.l.l["1"] = "<cmd>lua vim.lsp.codelens.run()<cr>"
lvim.builtin.which_key.mappings.l.l["2"] = "CodeLens Action"
lvim.builtin.which_key.mappings.L.l.d["1"] = "<cmd>lua require('lvim.core.terminal').toggle_log_view(require('lvim.core.log').get_path())<cr>"
lvim.builtin.which_key.mappings.L.l.D["1"] = "<cmd>lua vim.fn.execute('edit ' .. require('lvim.core.log').get_path())<cr>"
lvim.builtin.which_key.mappings.L.l.D["2"] = "Open the default logfile"
lvim.builtin.which_key.mappings.L.l.d["2"] = "view default log"
lvim.builtin.which_key.mappings.L.l.l["1"] = "<cmd>lua require('lvim.core.terminal').toggle_log_view(vim.lsp.get_log_path())<cr>"
lvim.builtin.which_key.mappings.L.l.L["1"] = "<cmd>lua vim.fn.execute('edit ' .. vim.lsp.get_log_path())<cr>"
lvim.builtin.which_key.mappings.L.l.L["2"] = "Open the LSP logfile"
lvim.builtin.which_key.mappings.L.l.l["2"] = "view lsp log"
lvim.builtin.which_key.mappings.L.l.N["1"] = "<cmd>edit $NVIM_LOG_FILE<cr>"
lvim.builtin.which_key.mappings.L.l.n["1"] = "<cmd>lua require('lvim.core.terminal').toggle_log_view(os.getenv('NVIM_LOG_FILE'))<cr>"
lvim.builtin.which_key.mappings.L.l.N["2"] = "Open the Neovim logfile"
lvim.builtin.which_key.mappings.L.l.n["2"] = "view neovim log"
lvim.builtin.which_key.mappings.L.l.name = "+logs"
lvim.builtin.which_key.mappings.l.name = "LSP"
lvim.builtin.which_key.mappings.L.name = "+LunarVim"
lvim.builtin.which_key.mappings.l.q["1"] = "<cmd>lua vim.diagnostic.setloclist()<cr>"
lvim.builtin.which_key.mappings.l.q["2"] = "Quickfix"
lvim.builtin.which_key.mappings.l.r["1"] = "<cmd>lua vim.lsp.buf.rename()<cr>"
lvim.builtin.which_key.mappings.L.r["1"] = "<cmd>LvimReload<cr>"
lvim.builtin.which_key.mappings.L.r["2"] = "Reload LunarVim's configuration"
lvim.builtin.which_key.mappings.l.r["2"] = "Rename"
lvim.builtin.which_key.mappings.l.s["1"] = "<cmd>Telescope lsp_document_symbols<cr>"
lvim.builtin.which_key.mappings.l.S["1"] = "<cmd>Telescope lsp_dynamic_workspace_symbols<cr>"
lvim.builtin.which_key.mappings.l.s["2"] = "Document Symbols"
lvim.builtin.which_key.mappings.l.S["2"] = "Workspace Symbols"
lvim.builtin.which_key.mappings.L.u["1"] = "<cmd>LvimUpdate<cr>"
lvim.builtin.which_key.mappings.L.u["2"] = "Update LunarVim"
lvim.builtin.which_key.mappings.l.w["1"] = "<cmd>Telescope diagnostics<cr>"
lvim.builtin.which_key.mappings.l.w["2"] = "Diagnostics"
lvim.builtin.which_key.mappings.p.c["1"] = "<cmd>Lazy clean<cr>"
lvim.builtin.which_key.mappings.p.c["2"] = "Clean"
lvim.builtin.which_key.mappings.p.d["1"] = "<cmd>Lazy debug<cr>"
lvim.builtin.which_key.mappings.p.d["2"] = "Debug"
lvim.builtin.which_key.mappings.p.i["1"] = "<cmd>Lazy install<cr>"
lvim.builtin.which_key.mappings.p.i["2"] = "Install"
lvim.builtin.which_key.mappings.p.l["1"] = "<cmd>Lazy log<cr>"
lvim.builtin.which_key.mappings.p.l["2"] = "Log"
lvim.builtin.which_key.mappings.p.name = "Plugins"
lvim.builtin.which_key.mappings.p.p["1"] = "<cmd>Lazy profile<cr>"
lvim.builtin.which_key.mappings.p.p["2"] = "Profile"
lvim.builtin.which_key.mappings.p.S["1"] = "<cmd>Lazy clear<cr>"
lvim.builtin.which_key.mappings.p.s["1"] = "<cmd>Lazy sync<cr>"
lvim.builtin.which_key.mappings.p.S["2"] = "Status"
lvim.builtin.which_key.mappings.p.s["2"] = "Sync"
lvim.builtin.which_key.mappings.p.u["1"] = "<cmd>Lazy update<cr>"
lvim.builtin.which_key.mappings.p.u["2"] = "Update"
lvim.builtin.which_key.mappings.q["1"] = "<cmd>confirm q<CR>"
lvim.builtin.which_key.mappings.q["2"] = "Quit"
lvim.builtin.which_key.mappings.s.b["1"] = "<cmd>Telescope git_branches<cr>"
lvim.builtin.which_key.mappings.s.b["2"] = "Checkout branch"
lvim.builtin.which_key.mappings.s.c["1"] = "<cmd>Telescope colorscheme<cr>"
lvim.builtin.which_key.mappings.s.C["1"] = "<cmd>Telescope commands<cr>"
lvim.builtin.which_key.mappings.s.c["2"] = "Colorscheme"
lvim.builtin.which_key.mappings.s.C["2"] = "Commands"
lvim.builtin.which_key.mappings.s.f["1"] = "<cmd>Telescope find_files<cr>"
lvim.builtin.which_key.mappings.s.f["2"] = "Find File"
lvim.builtin.which_key.mappings.s.h["1"] = "<cmd>Telescope help_tags<cr>"
lvim.builtin.which_key.mappings.s.H["1"] = "<cmd>Telescope highlights<cr>"
lvim.builtin.which_key.mappings.s.h["2"] = "Find Help"
lvim.builtin.which_key.mappings.s.H["2"] = "Find highlight groups"
lvim.builtin.which_key.mappings.s.k["1"] = "<cmd>Telescope keymaps<cr>"
lvim.builtin.which_key.mappings.s.k["2"] = "Keymaps"
lvim.builtin.which_key.mappings.s.l["1"] = "<cmd>Telescope resume<cr>"
lvim.builtin.which_key.mappings.s.l["2"] = "Resume last search"
lvim.builtin.which_key.mappings.s.M["1"] = "<cmd>Telescope man_pages<cr>"
lvim.builtin.which_key.mappings.s.M["2"] = "Man Pages"
lvim.builtin.which_key.mappings.s.name = "Search"
lvim.builtin.which_key.mappings.s.p["1"] = "<cmd>lua require('telescope.builtin').colorscheme({enable_preview = true})<cr>"
lvim.builtin.which_key.mappings.s.p["2"] = "Colorscheme with Preview"
lvim.builtin.which_key.mappings.s.r["1"] = "<cmd>Telescope oldfiles<cr>"
lvim.builtin.which_key.mappings.s.R["1"] = "<cmd>Telescope registers<cr>"
lvim.builtin.which_key.mappings.s.r["2"] = "Open Recent File"
lvim.builtin.which_key.mappings.s.R["2"] = "Registers"
lvim.builtin.which_key.mappings.s.t["1"] = "<cmd>Telescope live_grep<cr>"
lvim.builtin.which_key.mappings.s.t["2"] = "Text"
lvim.builtin.which_key.mappings.T.i["1"] = ":TSConfigInfo<cr>"
lvim.builtin.which_key.mappings.T.i["2"] = "Info"
lvim.builtin.which_key.mappings.T.name = "Treesitter"
lvim.builtin.which_key.mappings.w["1"] = "<cmd>w!<CR>"
lvim.builtin.which_key.mappings.w["2"] = "Save"
lvim.builtin.which_key.opts.mode = "n"
lvim.builtin.which_key.opts.noremap = true
lvim.builtin.which_key.opts.nowait = true
lvim.builtin.which_key.opts.prefix = "<leader>"
lvim.builtin.which_key.opts.silent = true
lvim.builtin.which_key.setup.disable.filetypes["1"] = "TelescopePrompt"
lvim.builtin.which_key.setup.hidden["1"] = "<silent>"
lvim.builtin.which_key.setup.hidden["2"] = "<cmd>"
lvim.builtin.which_key.setup.hidden["3"] = "<Cmd>"
lvim.builtin.which_key.setup.hidden["4"] = "<CR>"
lvim.builtin.which_key.setup.hidden["5"] = "call"
lvim.builtin.which_key.setup.hidden["6"] = "lua"
lvim.builtin.which_key.setup.hidden["7"] = "^:"
lvim.builtin.which_key.setup.hidden["8"] = "^ "
lvim.builtin.which_key.setup.icons.breadcrumb = "»"
lvim.builtin.which_key.setup.icons.group = ""
lvim.builtin.which_key.setup.icons.separator = ""
lvim.builtin.which_key.setup.ignore_missing = true
lvim.builtin.which_key.setup.layout.align = "left"
lvim.builtin.which_key.setup.layout.height.max = 25
lvim.builtin.which_key.setup.layout.height.min = 4
lvim.builtin.which_key.setup.layout.spacing = 3
lvim.builtin.which_key.setup.layout.width.max = 50
lvim.builtin.which_key.setup.layout.width.min = 20
lvim.builtin.which_key.setup.operators.gc = "Comments"
lvim.builtin.which_key.setup.plugins.marks = false
lvim.builtin.which_key.setup.plugins.presets.g = false
lvim.builtin.which_key.setup.plugins.presets.motions = false
lvim.builtin.which_key.setup.plugins.presets.nav = false
lvim.builtin.which_key.setup.plugins.presets.operators = false
lvim.builtin.which_key.setup.plugins.presets.text_objects = false
lvim.builtin.which_key.setup.plugins.presets.windows = false
lvim.builtin.which_key.setup.plugins.presets.z = false
lvim.builtin.which_key.setup.plugins.registers = false
lvim.builtin.which_key.setup.plugins.spelling.enabled = true
lvim.builtin.which_key.setup.plugins.spelling.suggestions = 20
lvim.builtin.which_key.setup.popup_mappings.scroll_down = "<c-d>"
lvim.builtin.which_key.setup.popup_mappings.scroll_up = "<c-u>"
lvim.builtin.which_key.setup.show_help = true
lvim.builtin.which_key.setup.show_keys = true
lvim.builtin.which_key.setup.triggers = "auto"
lvim.builtin.which_key.setup.triggers_blacklist.i["1"] = "j"
lvim.builtin.which_key.setup.triggers_blacklist.i["2"] = "k"
lvim.builtin.which_key.setup.triggers_blacklist.v["1"] = "j"
lvim.builtin.which_key.setup.triggers_blacklist.v["2"] = "k"
lvim.builtin.which_key.setup.window.border = "single"
lvim.builtin.which_key.setup.window.margin["1"] = 1
lvim.builtin.which_key.setup.window.margin["2"] = 0
lvim.builtin.which_key.setup.window.margin["3"] = 1
lvim.builtin.which_key.setup.window.margin["4"] = 0
lvim.builtin.which_key.setup.window.padding["1"] = 2
lvim.builtin.which_key.setup.window.padding["2"] = 2
lvim.builtin.which_key.setup.window.padding["3"] = 2
lvim.builtin.which_key.setup.window.padding["4"] = 2
lvim.builtin.which_key.setup.window.position = "bottom"
lvim.builtin.which_key.setup.window.winblend = 0
lvim.builtin.which_key.vmappings["/"]["1"] = "<Plug>(comment_toggle_linewise_visual)"
lvim.builtin.which_key.vmappings["/"]["2"] = "Comment toggle linewise (visual)"
lvim.builtin.which_key.vmappings.g.name = "Git"
lvim.builtin.which_key.vmappings.g.r["1"] = "<cmd>Gitsigns reset_hunk<cr>"
lvim.builtin.which_key.vmappings.g.r["2"] = "Reset Hunk"
lvim.builtin.which_key.vmappings.g.s["1"] = "<cmd>Gitsigns stage_hunk<cr>"
lvim.builtin.which_key.vmappings.g.s["2"] = "Stage Hunk"
lvim.builtin.which_key.vmappings.l.a["1"] = "<cmd>lua vim.lsp.buf.code_action()<cr>"
lvim.builtin.which_key.vmappings.l.a["2"] = "Code Action"
lvim.builtin.which_key.vmappings.l.name = "LSP"
lvim.builtin.which_key.vopts.mode = "v"
lvim.builtin.which_key.vopts.noremap = true
lvim.builtin.which_key.vopts.nowait = true
lvim.builtin.which_key.vopts.prefix = "<leader>"
lvim.builtin.which_key.vopts.silent = true
lvim.colorscheme = "lunar"
lvim.format_on_save.enabled = false
-- lvim.format_on_save.filter = function ()
lvim.format_on_save.pattern = "*"
lvim.format_on_save.timeout = 1000
lvim.icons.diagnostics.BoldError = ""
lvim.icons.diagnostics.BoldHint = ""
lvim.icons.diagnostics.BoldInformation = ""
lvim.icons.diagnostics.BoldQuestion = ""
lvim.icons.diagnostics.BoldWarning = ""
lvim.icons.diagnostics.Debug = ""
lvim.icons.diagnostics.Error = ""
lvim.icons.diagnostics.Hint = "󰌶"
lvim.icons.diagnostics.Information = ""
lvim.icons.diagnostics.Question = ""
lvim.icons.diagnostics.Trace = "✎"
lvim.icons.diagnostics.Warning = ""
lvim.icons.git.Branch = ""
lvim.icons.git.Diff = ""
lvim.icons.git.FileDeleted = ""
lvim.icons.git.FileIgnored = "◌"
lvim.icons.git.FileRenamed = ""
lvim.icons.git.FileStaged = "S"
lvim.icons.git.FileUnmerged = ""
lvim.icons.git.FileUnstaged = ""
lvim.icons.git.FileUntracked = "U"
lvim.icons.git.LineAdded = ""
lvim.icons.git.LineModified = ""
lvim.icons.git.LineRemoved = ""
lvim.icons.git.Octoface = ""
lvim.icons.git.Repo = ""
lvim.icons.kind.Array = ""
lvim.icons.kind.Boolean = ""
lvim.icons.kind.Class = ""
lvim.icons.kind.Color = ""
lvim.icons.kind.Constant = ""
lvim.icons.kind.Constructor = ""
lvim.icons.kind.Enum = ""
lvim.icons.kind.EnumMember = ""
lvim.icons.kind.Event = ""
lvim.icons.kind.Field = ""
lvim.icons.kind.File = ""
lvim.icons.kind.Folder = "󰉋"
lvim.icons.kind.Function = ""
lvim.icons.kind.Interface = ""
lvim.icons.kind.Key = ""
lvim.icons.kind.Keyword = ""
lvim.icons.kind.Method = ""
lvim.icons.kind.Module = ""
lvim.icons.kind.Namespace = ""
lvim.icons.kind.Null = "󰟢"
lvim.icons.kind.Number = ""
lvim.icons.kind.Object = ""
lvim.icons.kind.Operator = ""
lvim.icons.kind.Package = ""
lvim.icons.kind.Property = ""
lvim.icons.kind.Reference = ""
lvim.icons.kind.Snippet = ""
lvim.icons.kind.String = ""
lvim.icons.kind.Struct = ""
lvim.icons.kind.Text = ""
lvim.icons.kind.TypeParameter = ""
lvim.icons.kind.Unit = ""
lvim.icons.kind.Value = ""
lvim.icons.kind.Variable = ""
lvim.icons.misc.CircuitBoard = ""
lvim.icons.misc.Package = ""
lvim.icons.misc.Robot = "󰚩"
lvim.icons.misc.Smiley = ""
lvim.icons.misc.Squirrel = ""
lvim.icons.misc.Tag = ""
lvim.icons.misc.Watch = ""
lvim.icons.ui.ArrowCircleDown = ""
lvim.icons.ui.ArrowCircleLeft = ""
lvim.icons.ui.ArrowCircleRight = ""
lvim.icons.ui.ArrowCircleUp = ""
lvim.icons.ui.BoldArrowDown = ""
lvim.icons.ui.BoldArrowLeft = ""
lvim.icons.ui.BoldArrowRight = ""
lvim.icons.ui.BoldArrowUp = ""
lvim.icons.ui.BoldClose = ""
lvim.icons.ui.BoldDividerLeft = ""
lvim.icons.ui.BoldDividerRight = ""
lvim.icons.ui.BoldLineLeft = "▎"
lvim.icons.ui.BookMark = ""
lvim.icons.ui.BoxChecked = ""
lvim.icons.ui.Bug = ""
lvim.icons.ui.Calendar = ""
lvim.icons.ui.Check = ""
lvim.icons.ui.ChevronRight = ""
lvim.icons.ui.ChevronShortDown = ""
lvim.icons.ui.ChevronShortLeft = ""
lvim.icons.ui.ChevronShortRight = ""
lvim.icons.ui.ChevronShortUp = ""
lvim.icons.ui.Circle = " "
lvim.icons.ui.Close = "󰅖"
lvim.icons.ui.CloudDownload = ""
lvim.icons.ui.Code = ""
lvim.icons.ui.Comment = ""
lvim.icons.ui.Dashboard = ""
lvim.icons.ui.DebugConsole = ""
lvim.icons.ui.DividerLeft = ""
lvim.icons.ui.DividerRight = ""
lvim.icons.ui.DoubleChevronRight = "»"
lvim.icons.ui.Ellipsis = ""
lvim.icons.ui.EmptyFolder = ""
lvim.icons.ui.EmptyFolderOpen = ""
lvim.icons.ui.File = ""
lvim.icons.ui.Files = ""
lvim.icons.ui.FileSymlink = ""
lvim.icons.ui.FindFile = "󰈞"
lvim.icons.ui.FindText = "󰊄"
lvim.icons.ui.Fire = ""
lvim.icons.ui.Folder = "󰉋"
lvim.icons.ui.FolderOpen = ""
lvim.icons.ui.FolderSymlink = ""
lvim.icons.ui.Forward = ""
lvim.icons.ui.Gear = ""
lvim.icons.ui.History = ""
lvim.icons.ui.Lightbulb = ""
lvim.icons.ui.LineLeft = "▏"
lvim.icons.ui.LineMiddle = "│"
lvim.icons.ui.List = ""
lvim.icons.ui.Lock = ""
lvim.icons.ui.NewFile = ""
lvim.icons.ui.Note = ""
lvim.icons.ui.Package = ""
lvim.icons.ui.Pencil = "󰏫"
lvim.icons.ui.Plus = ""
lvim.icons.ui.Project = ""
lvim.icons.ui.Scopes = ""
lvim.icons.ui.Search = ""
lvim.icons.ui.SignIn = ""
lvim.icons.ui.SignOut = ""
lvim.icons.ui.Stacks = ""
lvim.icons.ui.Tab = "󰌒"
lvim.icons.ui.Table = ""
lvim.icons.ui.Target = "󰀘"
lvim.icons.ui.Telescope = ""
lvim.icons.ui.Text = ""
lvim.icons.ui.Tree = ""
lvim.icons.ui.Triangle = "󰐊"
lvim.icons.ui.TriangleShortArrowDown = ""
lvim.icons.ui.TriangleShortArrowLeft = ""
lvim.icons.ui.TriangleShortArrowRight = ""
lvim.icons.ui.TriangleShortArrowUp = ""
lvim.icons.ui.Watches = "󰂥"
lvim.lazy.opts.defaults.lazy = false
lvim.lazy.opts.git.timeout = 120
lvim.lazy.opts.install.colorscheme["1"] = "lunar"
lvim.lazy.opts.install.colorscheme["2"] = "lunar"
lvim.lazy.opts.install.colorscheme["3"] = "habamax"
lvim.lazy.opts.install.colorscheme["4"] = "habamax"
lvim.lazy.opts.install.missing = true
lvim.lazy.opts.lockfile = "/home/<USER>/.config/lvim/lazy-lock.json"
lvim.lazy.opts.performance.rtp.reset = false
lvim.lazy.opts.readme.root = "/home/<USER>/.local/share/lunarvim/lazy/readme"
lvim.lazy.opts.root = "/home/<USER>/.local/share/lunarvim/site/pack/lazy/opt"
lvim.lazy.opts.spec["1"]["10"]["1"] = "nvim-lua/plenary.nvim"
lvim.lazy.opts.spec["1"]["10"].cmd["1"] = "PlenaryBustedFile"
lvim.lazy.opts.spec["1"]["10"].cmd["2"] = "PlenaryBustedDirectory"
lvim.lazy.opts.spec["1"]["10"].commit = "a3e3bc8"
lvim.lazy.opts.spec["1"]["10"].lazy = true
lvim.lazy.opts.spec["1"]["11"]["1"] = "nvim-telescope/telescope.nvim"
lvim.lazy.opts.spec["1"]["11"].branch = "0.1.x"
lvim.lazy.opts.spec["1"]["11"].cmd = "Telescope"
lvim.lazy.opts.spec["1"]["11"].commit = "d829aa6"
-- lvim.lazy.opts.spec["1"]["11"].config = function ()
lvim.lazy.opts.spec["1"]["11"].dependencies["1"] = "telescope-fzf-native.nvim"
lvim.lazy.opts.spec["1"]["11"].enabled = true
lvim.lazy.opts.spec["1"]["1"]["1"] = "folke/lazy.nvim"
lvim.lazy.opts.spec["1"]["11"].lazy = true
lvim.lazy.opts.spec["1"]["12"]["1"] = "nvim-telescope/telescope-fzf-native.nvim"
lvim.lazy.opts.spec["1"]["12"].build = "make"
lvim.lazy.opts.spec["1"]["12"].commit = "9ef21b2"
lvim.lazy.opts.spec["1"]["12"].enabled = true
lvim.lazy.opts.spec["1"]["12"].lazy = true
lvim.lazy.opts.spec["1"]["13"]["1"] = "hrsh7th/nvim-cmp"
lvim.lazy.opts.spec["1"]["13"].commit = "5260e5e"
-- lvim.lazy.opts.spec["1"]["13"].config = function ()
lvim.lazy.opts.spec["1"]["13"].dependencies["1"] = "cmp-nvim-lsp"
lvim.lazy.opts.spec["1"]["13"].dependencies["2"] = "cmp_luasnip"
lvim.lazy.opts.spec["1"]["13"].dependencies["3"] = "cmp-buffer"
lvim.lazy.opts.spec["1"]["13"].dependencies["4"] = "cmp-path"
lvim.lazy.opts.spec["1"]["13"].dependencies["5"] = "cmp-cmdline"
lvim.lazy.opts.spec["1"]["13"].event["1"] = "InsertEnter"
lvim.lazy.opts.spec["1"]["13"].event["2"] = "CmdlineEnter"
lvim.lazy.opts.spec["1"]["14"]["1"] = "hrsh7th/cmp-nvim-lsp"
lvim.lazy.opts.spec["1"]["14"].commit = "39e2eda"
lvim.lazy.opts.spec["1"]["14"].lazy = true
lvim.lazy.opts.spec["1"]["15"]["1"] = "saadparwaiz1/cmp_luasnip"
lvim.lazy.opts.spec["1"]["15"].commit = "05a9ab2"
lvim.lazy.opts.spec["1"]["15"].lazy = true
lvim.lazy.opts.spec["1"]["16"]["1"] = "hrsh7th/cmp-buffer"
lvim.lazy.opts.spec["1"]["16"].commit = "3022dbc"
lvim.lazy.opts.spec["1"]["16"].lazy = true
lvim.lazy.opts.spec["1"]["17"]["1"] = "hrsh7th/cmp-path"
lvim.lazy.opts.spec["1"]["17"].commit = "91ff86c"
lvim.lazy.opts.spec["1"]["17"].lazy = true
lvim.lazy.opts.spec["1"]["18"]["1"] = "hrsh7th/cmp-cmdline"
lvim.lazy.opts.spec["1"]["18"].commit = "d250c63"
lvim.lazy.opts.spec["1"]["18"].enabled = false
lvim.lazy.opts.spec["1"]["18"].lazy = true
lvim.lazy.opts.spec["1"]["19"]["1"] = "L3MON4D3/LuaSnip"
lvim.lazy.opts.spec["1"]["19"].commit = "1def353"
-- lvim.lazy.opts.spec["1"]["19"].config = function ()
lvim.lazy.opts.spec["1"]["19"].dependencies["1"] = "friendly-snippets"
lvim.lazy.opts.spec["1"]["19"].event = "InsertEnter"
lvim.lazy.opts.spec["1"]["1"].commit = "8f19915"
lvim.lazy.opts.spec["1"]["1"].tag = "stable"
lvim.lazy.opts.spec["1"]["20"]["1"] = "rafamadriz/friendly-snippets"
lvim.lazy.opts.spec["1"]["20"].commit = "dd2fd12"
lvim.lazy.opts.spec["1"]["20"].cond = true
lvim.lazy.opts.spec["1"]["20"].lazy = true
lvim.lazy.opts.spec["1"]["21"]["1"] = "folke/neodev.nvim"
lvim.lazy.opts.spec["1"]["21"].commit = "ce9a2e8"
lvim.lazy.opts.spec["1"]["21"].lazy = true
lvim.lazy.opts.spec["1"]["2"]["1"] = "neovim/nvim-lspconfig"
lvim.lazy.opts.spec["1"]["22"]["1"] = "windwp/nvim-autopairs"
lvim.lazy.opts.spec["1"]["22"].commit = "c15de7e"
-- lvim.lazy.opts.spec["1"]["22"].config = function ()
lvim.lazy.opts.spec["1"]["22"].dependencies["1"] = "nvim-treesitter/nvim-treesitter"
lvim.lazy.opts.spec["1"]["22"].dependencies["2"] = "hrsh7th/nvim-cmp"
lvim.lazy.opts.spec["1"]["22"].enabled = true
lvim.lazy.opts.spec["1"]["22"].event = "InsertEnter"
lvim.lazy.opts.spec["1"]["23"]["1"] = "nvim-treesitter/nvim-treesitter"
lvim.lazy.opts.spec["1"]["23"].cmd["1"] = "TSInstall"
lvim.lazy.opts.spec["1"]["23"].cmd["2"] = "TSUninstall"
lvim.lazy.opts.spec["1"]["23"].cmd["3"] = "TSUpdate"
lvim.lazy.opts.spec["1"]["23"].cmd["4"] = "TSUpdateSync"
lvim.lazy.opts.spec["1"]["23"].cmd["5"] = "TSInstallInfo"
lvim.lazy.opts.spec["1"]["23"].cmd["6"] = "TSInstallSync"
lvim.lazy.opts.spec["1"]["23"].cmd["7"] = "TSInstallFromGrammar"
lvim.lazy.opts.spec["1"]["23"].commit = "30de5e7"
-- lvim.lazy.opts.spec["1"]["23"].config = function ()
lvim.lazy.opts.spec["1"]["23"].event = "User FileOpened"
lvim.lazy.opts.spec["1"]["24"]["1"] = "JoosepAlviste/nvim-ts-context-commentstring"
lvim.lazy.opts.spec["1"]["24"].commit = "cb06438"
lvim.lazy.opts.spec["1"]["24"].lazy = true
lvim.lazy.opts.spec["1"]["25"]["1"] = "nvim-tree/nvim-tree.lua"
lvim.lazy.opts.spec["1"]["25"].cmd["1"] = "NvimTreeToggle"
lvim.lazy.opts.spec["1"]["25"].cmd["2"] = "NvimTreeOpen"
lvim.lazy.opts.spec["1"]["25"].cmd["3"] = "NvimTreeFocus"
lvim.lazy.opts.spec["1"]["25"].cmd["4"] = "NvimTreeFindFileToggle"
lvim.lazy.opts.spec["1"]["25"].commit = "2bc725a"
-- lvim.lazy.opts.spec["1"]["25"].config = function ()
lvim.lazy.opts.spec["1"]["25"].enabled = true
lvim.lazy.opts.spec["1"]["25"].event = "User DirOpened"
lvim.lazy.opts.spec["1"]["26"]["1"] = "tamago324/lir.nvim"
lvim.lazy.opts.spec["1"]["26"].commit = "7a9d45d"
-- lvim.lazy.opts.spec["1"]["26"].config = function ()
lvim.lazy.opts.spec["1"]["26"].enabled = true
lvim.lazy.opts.spec["1"]["26"].event = "User DirOpened"
lvim.lazy.opts.spec["1"]["27"]["1"] = "lewis6991/gitsigns.nvim"
lvim.lazy.opts.spec["1"]["27"].cmd = "Gitsigns"
lvim.lazy.opts.spec["1"]["27"].commit = "805610a"
-- lvim.lazy.opts.spec["1"]["27"].config = function ()
lvim.lazy.opts.spec["1"]["27"].enabled = true
lvim.lazy.opts.spec["1"]["27"].event = "User FileOpened"
lvim.lazy.opts.spec["1"]["28"]["1"] = "folke/which-key.nvim"
lvim.lazy.opts.spec["1"]["28"].cmd = "WhichKey"
lvim.lazy.opts.spec["1"]["28"].commit = "4433e5e"
-- lvim.lazy.opts.spec["1"]["28"].config = function ()
lvim.lazy.opts.spec["1"]["28"].enabled = true
lvim.lazy.opts.spec["1"]["28"].event = "VeryLazy"
lvim.lazy.opts.spec["1"]["29"]["1"] = "numToStr/Comment.nvim"
lvim.lazy.opts.spec["1"]["29"].commit = "0236521"
-- lvim.lazy.opts.spec["1"]["29"].config = function ()
lvim.lazy.opts.spec["1"]["29"].enabled = true
lvim.lazy.opts.spec["1"]["29"].event = "User FileOpened"
lvim.lazy.opts.spec["1"]["29"].keys["1"]["1"] = "gc"
lvim.lazy.opts.spec["1"]["29"].keys["1"].mode["1"] = "n"
lvim.lazy.opts.spec["1"]["29"].keys["1"].mode["2"] = "v"
lvim.lazy.opts.spec["1"]["29"].keys["2"]["1"] = "gb"
lvim.lazy.opts.spec["1"]["29"].keys["2"].mode["1"] = "n"
lvim.lazy.opts.spec["1"]["29"].keys["2"].mode["2"] = "v"
lvim.lazy.opts.spec["1"]["2"].commit = "eadcee1"
lvim.lazy.opts.spec["1"]["2"].dependencies["1"] = "mason-lspconfig.nvim"
lvim.lazy.opts.spec["1"]["2"].dependencies["2"] = "nlsp-settings.nvim"
lvim.lazy.opts.spec["1"]["2"].lazy = true
lvim.lazy.opts.spec["1"]["30"]["1"] = "ahmedkhalf/project.nvim"
lvim.lazy.opts.spec["1"]["30"].cmd = "Telescope projects"
lvim.lazy.opts.spec["1"]["30"].commit = "8c6bad7"
-- lvim.lazy.opts.spec["1"]["30"].config = function ()
lvim.lazy.opts.spec["1"]["30"].enabled = true
lvim.lazy.opts.spec["1"]["30"].event = "VimEnter"
lvim.lazy.opts.spec["1"]["31"]["1"] = "nvim-tree/nvim-web-devicons"
lvim.lazy.opts.spec["1"]["31"].commit = "e37bb1f"
lvim.lazy.opts.spec["1"]["31"].enabled = true
lvim.lazy.opts.spec["1"]["31"].lazy = true
lvim.lazy.opts.spec["1"]["3"]["1"] = "williamboman/mason-lspconfig.nvim"
lvim.lazy.opts.spec["1"]["32"]["1"] = "nvim-lualine/lualine.nvim"
lvim.lazy.opts.spec["1"]["32"].commit = "0a5a668"
-- lvim.lazy.opts.spec["1"]["32"].config = function ()
lvim.lazy.opts.spec["1"]["32"].enabled = true
lvim.lazy.opts.spec["1"]["32"].event = "VimEnter"
lvim.lazy.opts.spec["1"]["33"]["1"] = "SmiteshP/nvim-navic"
lvim.lazy.opts.spec["1"]["33"].commit = "8649f69"
-- lvim.lazy.opts.spec["1"]["33"].config = function ()
lvim.lazy.opts.spec["1"]["33"].enabled = true
lvim.lazy.opts.spec["1"]["33"].event = "User FileOpened"
lvim.lazy.opts.spec["1"]["34"]["1"] = "akinsho/bufferline.nvim"
lvim.lazy.opts.spec["1"]["34"].branch = "main"
lvim.lazy.opts.spec["1"]["34"].commit = "73edc1f"
-- lvim.lazy.opts.spec["1"]["34"].config = function ()
lvim.lazy.opts.spec["1"]["34"].enabled = true
lvim.lazy.opts.spec["1"]["34"].event = "User FileOpened"
lvim.lazy.opts.spec["1"]["35"]["1"] = "mfussenegger/nvim-dap"
lvim.lazy.opts.spec["1"]["35"].commit = "5a2f712"
-- lvim.lazy.opts.spec["1"]["35"].config = function ()
lvim.lazy.opts.spec["1"]["35"].dependencies["1"] = "rcarriga/nvim-dap-ui"
lvim.lazy.opts.spec["1"]["35"].enabled = true
lvim.lazy.opts.spec["1"]["35"].lazy = true
lvim.lazy.opts.spec["1"]["36"]["1"] = "rcarriga/nvim-dap-ui"
lvim.lazy.opts.spec["1"]["36"].commit = "34160a7"
-- lvim.lazy.opts.spec["1"]["36"].config = function ()
lvim.lazy.opts.spec["1"]["36"].enabled = true
lvim.lazy.opts.spec["1"]["36"].lazy = true
lvim.lazy.opts.spec["1"]["37"]["1"] = "goolord/alpha-nvim"
lvim.lazy.opts.spec["1"]["37"].commit = "29074ee"
-- lvim.lazy.opts.spec["1"]["37"].config = function ()
lvim.lazy.opts.spec["1"]["37"].enabled = true
lvim.lazy.opts.spec["1"]["37"].event = "VimEnter"
lvim.lazy.opts.spec["1"]["38"]["1"] = "akinsho/toggleterm.nvim"
lvim.lazy.opts.spec["1"]["38"].branch = "main"
lvim.lazy.opts.spec["1"]["38"].cmd["1"] = "ToggleTerm"
lvim.lazy.opts.spec["1"]["38"].cmd["2"] = "TermExec"
lvim.lazy.opts.spec["1"]["38"].cmd["3"] = "ToggleTermToggleAll"
lvim.lazy.opts.spec["1"]["38"].cmd["4"] = "ToggleTermSendCurrentLine"
lvim.lazy.opts.spec["1"]["38"].cmd["5"] = "ToggleTermSendVisualLines"
lvim.lazy.opts.spec["1"]["38"].cmd["6"] = "ToggleTermSendVisualSelection"
lvim.lazy.opts.spec["1"]["38"].commit = "fee58a0"
-- lvim.lazy.opts.spec["1"]["38"].config = function ()
lvim.lazy.opts.spec["1"]["38"].enabled = true
-- lvim.lazy.opts.spec["1"]["38"].init = function ()
lvim.lazy.opts.spec["1"]["38"].keys = "<c-\\>"
lvim.lazy.opts.spec["1"]["39"]["1"] = "b0o/schemastore.nvim"
lvim.lazy.opts.spec["1"]["39"].commit = "9a5992a"
lvim.lazy.opts.spec["1"]["39"].lazy = true
lvim.lazy.opts.spec["1"]["3"].cmd["1"] = "LspInstall"
lvim.lazy.opts.spec["1"]["3"].cmd["2"] = "LspUninstall"
lvim.lazy.opts.spec["1"]["3"].commit = "a4caa0d"
-- lvim.lazy.opts.spec["1"]["3"].config = function ()
lvim.lazy.opts.spec["1"]["3"].dependencies = "mason.nvim"
lvim.lazy.opts.spec["1"]["3"].event = "User FileOpened"
lvim.lazy.opts.spec["1"]["3"].lazy = true
lvim.lazy.opts.spec["1"]["40"]["1"] = "RRethy/vim-illuminate"
lvim.lazy.opts.spec["1"]["40"].commit = "5eeb795"
-- lvim.lazy.opts.spec["1"]["40"].config = function ()
lvim.lazy.opts.spec["1"]["40"].enabled = true
lvim.lazy.opts.spec["1"]["40"].event = "User FileOpened"
lvim.lazy.opts.spec["1"]["41"]["1"] = "lukas-reineke/indent-blankline.nvim"
lvim.lazy.opts.spec["1"]["41"].commit = "9637670"
-- lvim.lazy.opts.spec["1"]["41"].config = function ()
lvim.lazy.opts.spec["1"]["41"].enabled = true
lvim.lazy.opts.spec["1"]["41"].event = "User FileOpened"
lvim.lazy.opts.spec["1"]["4"]["1"] = "tamago324/nlsp-settings.nvim"
lvim.lazy.opts.spec["1"]["42"]["1"] = "lunarvim/onedarker.nvim"
lvim.lazy.opts.spec["1"]["42"].branch = "freeze"
lvim.lazy.opts.spec["1"]["42"].commit = "b00dd21"
-- lvim.lazy.opts.spec["1"]["42"].config = function ()
lvim.lazy.opts.spec["1"]["42"].lazy = true
lvim.lazy.opts.spec["1"]["43"]["1"] = "lunarvim/bigfile.nvim"
lvim.lazy.opts.spec["1"]["43"].commit = "33eb067"
-- lvim.lazy.opts.spec["1"]["43"].config = function ()
lvim.lazy.opts.spec["1"]["43"].dependencies["1"] = "nvim-treesitter/nvim-treesitter"
lvim.lazy.opts.spec["1"]["43"].enabled = true
lvim.lazy.opts.spec["1"]["43"].event["1"] = "FileReadPre"
lvim.lazy.opts.spec["1"]["43"].event["2"] = "BufReadPre"
lvim.lazy.opts.spec["1"]["43"].event["3"] = "User FileOpened"
lvim.lazy.opts.spec["1"]["4"].cmd = "LspSettings"
lvim.lazy.opts.spec["1"]["4"].commit = "d92035e"
lvim.lazy.opts.spec["1"]["4"].lazy = true
lvim.lazy.opts.spec["1"]["5"]["1"] = "nvimtools/none-ls.nvim"
lvim.lazy.opts.spec["1"]["5"].commit = "3a48266"
lvim.lazy.opts.spec["1"]["5"].lazy = true
lvim.lazy.opts.spec["1"]["6"]["1"] = "williamboman/mason.nvim"
-- lvim.lazy.opts.spec["1"]["6"].build = function ()
lvim.lazy.opts.spec["1"]["6"].cmd["1"] = "Mason"
lvim.lazy.opts.spec["1"]["6"].cmd["2"] = "MasonInstall"
lvim.lazy.opts.spec["1"]["6"].cmd["3"] = "MasonUninstall"
lvim.lazy.opts.spec["1"]["6"].cmd["4"] = "MasonUninstallAll"
lvim.lazy.opts.spec["1"]["6"].cmd["5"] = "MasonLog"
lvim.lazy.opts.spec["1"]["6"].commit = "49ff59a"
-- lvim.lazy.opts.spec["1"]["6"].config = function ()
lvim.lazy.opts.spec["1"]["6"].event = "User FileOpened"
lvim.lazy.opts.spec["1"]["6"].lazy = true
lvim.lazy.opts.spec["1"]["7"]["1"] = "folke/tokyonight.nvim"
lvim.lazy.opts.spec["1"]["7"].commit = "b9b494f"
lvim.lazy.opts.spec["1"]["7"].lazy = true
lvim.lazy.opts.spec["1"]["8"]["1"] = "lunarvim/lunar.nvim"
lvim.lazy.opts.spec["1"]["8"].commit = "08bbc93"
lvim.lazy.opts.spec["1"]["8"].lazy = false
lvim.lazy.opts.spec["1"]["9"]["1"] = "Tastyep/structlog.nvim"
lvim.lazy.opts.spec["1"]["9"].commit = "45b26a2"
lvim.lazy.opts.spec["1"]["9"].lazy = true
lvim.lazy.opts.ui.border = "rounded"
lvim.leader = "space"
lvim.log.level = "info"
lvim.log.override_notify = false
lvim.log.viewer.cmd = "lnav"
lvim.log.viewer.layout_config.direction = "horizontal"
lvim.log.viewer.layout_config.open_mapping = ""
lvim.log.viewer.layout_config.size = 40
lvim.lsp.automatic_configuration.skipped_filetypes["1"] = "markdown"
lvim.lsp.automatic_configuration.skipped_filetypes["2"] = "rst"
lvim.lsp.automatic_configuration.skipped_filetypes["3"] = "plaintext"
lvim.lsp.automatic_configuration.skipped_filetypes["4"] = "toml"
lvim.lsp.automatic_configuration.skipped_filetypes["5"] = "proto"
lvim.lsp.automatic_configuration.skipped_servers["10"] = "css_variables"
lvim.lsp.automatic_configuration.skipped_servers["11"] = "cssmodules_ls"
lvim.lsp.automatic_configuration.skipped_servers["12"] = "custom_elements_ls"
lvim.lsp.automatic_configuration.skipped_servers["13"] = "denols"
lvim.lsp.automatic_configuration.skipped_servers["14"] = "docker_compose_language_service"
lvim.lsp.automatic_configuration.skipped_servers["15"] = "dprint"
lvim.lsp.automatic_configuration.skipped_servers["16"] = "elp"
lvim.lsp.automatic_configuration.skipped_servers["17"] = "ember"
lvim.lsp.automatic_configuration.skipped_servers["18"] = "emmet_language_server"
lvim.lsp.automatic_configuration.skipped_servers["19"] = "emmet_ls"
lvim.lsp.automatic_configuration.skipped_servers["1"] = "angularls"
lvim.lsp.automatic_configuration.skipped_servers["20"] = "eslint"
lvim.lsp.automatic_configuration.skipped_servers["21"] = "eslintls"
lvim.lsp.automatic_configuration.skipped_servers["22"] = "fennel_language_server"
lvim.lsp.automatic_configuration.skipped_servers["23"] = "gitlab_ci_ls"
lvim.lsp.automatic_configuration.skipped_servers["24"] = "glint"
lvim.lsp.automatic_configuration.skipped_servers["25"] = "glslls"
lvim.lsp.automatic_configuration.skipped_servers["26"] = "golangci_lint_ls"
lvim.lsp.automatic_configuration.skipped_servers["27"] = "gradle_ls"
lvim.lsp.automatic_configuration.skipped_servers["28"] = "graphql"
lvim.lsp.automatic_configuration.skipped_servers["29"] = "harper_ls"
lvim.lsp.automatic_configuration.skipped_servers["2"] = "ansiblels"
lvim.lsp.automatic_configuration.skipped_servers["30"] = "hdl_checker"
lvim.lsp.automatic_configuration.skipped_servers["31"] = "hydra_lsp"
lvim.lsp.automatic_configuration.skipped_servers["32"] = "htmx"
lvim.lsp.automatic_configuration.skipped_servers["33"] = "java_language_server"
lvim.lsp.automatic_configuration.skipped_servers["34"] = "jedi_language_server"
lvim.lsp.automatic_configuration.skipped_servers["35"] = "lexical"
lvim.lsp.automatic_configuration.skipped_servers["36"] = "ltex"
lvim.lsp.automatic_configuration.skipped_servers["37"] = "lwc_ls"
lvim.lsp.automatic_configuration.skipped_servers["38"] = "mdx_analyzer"
lvim.lsp.automatic_configuration.skipped_servers["39"] = "neocmake"
lvim.lsp.automatic_configuration.skipped_servers["3"] = "antlersls"
lvim.lsp.automatic_configuration.skipped_servers["40"] = "nim_langserver"
lvim.lsp.automatic_configuration.skipped_servers["41"] = "ocamlls"
lvim.lsp.automatic_configuration.skipped_servers["42"] = "omnisharp"
lvim.lsp.automatic_configuration.skipped_servers["43"] = "phpactor"
lvim.lsp.automatic_configuration.skipped_servers["44"] = "psalm"
lvim.lsp.automatic_configuration.skipped_servers["45"] = "pylsp"
lvim.lsp.automatic_configuration.skipped_servers["46"] = "pylyzer"
lvim.lsp.automatic_configuration.skipped_servers["47"] = "pyre"
lvim.lsp.automatic_configuration.skipped_servers["48"] = "quick_lint_js"
lvim.lsp.automatic_configuration.skipped_servers["49"] = "reason_ls"
lvim.lsp.automatic_configuration.skipped_servers["4"] = "ast_grep"
lvim.lsp.automatic_configuration.skipped_servers["50"] = "rnix"
lvim.lsp.automatic_configuration.skipped_servers["51"] = "rome"
lvim.lsp.automatic_configuration.skipped_servers["52"] = "rubocop"
lvim.lsp.automatic_configuration.skipped_servers["53"] = "ruby_ls"
lvim.lsp.automatic_configuration.skipped_servers["54"] = "ruby_lsp"
lvim.lsp.automatic_configuration.skipped_servers["55"] = "ruff_lsp"
lvim.lsp.automatic_configuration.skipped_servers["56"] = "scry"
lvim.lsp.automatic_configuration.skipped_servers["57"] = "snyk_ls"
lvim.lsp.automatic_configuration.skipped_servers["58"] = "solang"
lvim.lsp.automatic_configuration.skipped_servers["59"] = "solc"
lvim.lsp.automatic_configuration.skipped_servers["5"] = "azure_pipelines_ls"
lvim.lsp.automatic_configuration.skipped_servers["60"] = "solidity_ls"
lvim.lsp.automatic_configuration.skipped_servers["61"] = "solidity_ls_nomicfoundation"
lvim.lsp.automatic_configuration.skipped_servers["62"] = "sorbet"
lvim.lsp.automatic_configuration.skipped_servers["63"] = "sourcekit"
lvim.lsp.automatic_configuration.skipped_servers["64"] = "somesass_ls"
lvim.lsp.automatic_configuration.skipped_servers["65"] = "sourcery"
lvim.lsp.automatic_configuration.skipped_servers["66"] = "spectral"
lvim.lsp.automatic_configuration.skipped_servers["67"] = "sqlls"
lvim.lsp.automatic_configuration.skipped_servers["68"] = "sqls"
lvim.lsp.automatic_configuration.skipped_servers["69"] = "standardrb"
lvim.lsp.automatic_configuration.skipped_servers["6"] = "basedpyright"
lvim.lsp.automatic_configuration.skipped_servers["70"] = "stimulus_ls"
lvim.lsp.automatic_configuration.skipped_servers["71"] = "stylelint_lsp"
lvim.lsp.automatic_configuration.skipped_servers["72"] = "svlangserver"
lvim.lsp.automatic_configuration.skipped_servers["73"] = "swift_mesonls"
lvim.lsp.automatic_configuration.skipped_servers["74"] = "templ"
lvim.lsp.automatic_configuration.skipped_servers["75"] = "tflint"
lvim.lsp.automatic_configuration.skipped_servers["76"] = "tinymist"
lvim.lsp.automatic_configuration.skipped_servers["77"] = "unocss"
lvim.lsp.automatic_configuration.skipped_servers["78"] = "vale_ls"
lvim.lsp.automatic_configuration.skipped_servers["79"] = "vacuum"
lvim.lsp.automatic_configuration.skipped_servers["7"] = "biome"
lvim.lsp.automatic_configuration.skipped_servers["80"] = "verible"
lvim.lsp.automatic_configuration.skipped_servers["81"] = "v_analyzer"
lvim.lsp.automatic_configuration.skipped_servers["82"] = "vtsls"
lvim.lsp.automatic_configuration.skipped_servers["83"] = "vuels"
lvim.lsp.automatic_configuration.skipped_servers["8"] = "bzl"
lvim.lsp.automatic_configuration.skipped_servers["9"] = "ccls"
lvim.lsp.buffer_mappings.normal_mode.gD["1"] = "<cmd>lua vim.lsp.buf.declaration()<cr>"
lvim.lsp.buffer_mappings.normal_mode.gd["1"] = "<cmd>lua vim.lsp.buf.definition()<cr>"
lvim.lsp.buffer_mappings.normal_mode.gD["2"] = "Goto Declaration"
lvim.lsp.buffer_mappings.normal_mode.gd["2"] = "Goto definition"
lvim.lsp.buffer_mappings.normal_mode.gI["1"] = "<cmd>lua vim.lsp.buf.implementation()<cr>"
lvim.lsp.buffer_mappings.normal_mode.gI["2"] = "Goto Implementation"
-- lvim.lsp.buffer_mappings.normal_mode.gl["1"] = function ()
lvim.lsp.buffer_mappings.normal_mode.gl["2"] = "Show line diagnostics"
lvim.lsp.buffer_mappings.normal_mode.gr["1"] = "<cmd>lua vim.lsp.buf.references()<cr>"
lvim.lsp.buffer_mappings.normal_mode.gr["2"] = "Goto references"
lvim.lsp.buffer_mappings.normal_mode.gs["1"] = "<cmd>lua vim.lsp.buf.signature_help()<cr>"
lvim.lsp.buffer_mappings.normal_mode.gs["2"] = "show signature help"
lvim.lsp.buffer_mappings.normal_mode.K["1"] = "<cmd>lua vim.lsp.buf.hover()<cr>"
lvim.lsp.buffer_mappings.normal_mode.K["2"] = "Show hover"
lvim.lsp.buffer_options.formatexpr = "v:lua.vim.lsp.formatexpr(#{timeout_ms:500})"
lvim.lsp.buffer_options.omnifunc = "v:lua.vim.lsp.omnifunc"
lvim.lsp.code_lens_refresh = true
lvim.lsp.document_highlight = false
lvim.lsp.nlsp_settings.setup.append_default_schemas = true
lvim.lsp.nlsp_settings.setup.config_home = "/home/<USER>/.config/lvim/lsp-settings"
lvim.lsp.nlsp_settings.setup.loader = "json"
lvim.lsp.null_ls.setup.debug = false
lvim.lsp.templates_dir = "/home/<USER>/.local/share/lunarvim/site/after/ftplugin"
lvim.reload_config_on_save = true
lvim.transparent_window = false
lvim.use_icons = true
