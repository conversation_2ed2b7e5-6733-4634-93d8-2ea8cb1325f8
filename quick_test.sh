#!/bin/bash
# ~/.config/lvim/quick_test.sh
# 快速测试 LunarVim 配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试配置文件语法
test_config_syntax() {
    log_info "测试配置文件语法..."
    
    local config_files=(
        "config.lua"
        "lua/user/options.lua"
        "lua/user/plugins.lua"
        "lua/user/keymaps.lua"
        "lua/user/languages/init.lua"
        "lua/user/ai/init.lua"
    )
    
    local errors=0
    
    for file in "${config_files[@]}"; do
        if [[ -f "$file" ]]; then
            if luac -p "$file" >/dev/null 2>&1; then
                log_success "$file 语法正确"
            else
                log_error "$file 语法错误"
                errors=$((errors + 1))
            fi
        else
            log_warning "$file 不存在"
        fi
    done
    
    return $errors
}

# 测试 LunarVim 启动
test_lunarvim_startup() {
    log_info "测试 LunarVim 启动..."
    
    local start_time=$(date +%s.%N)
    
    if timeout 30 lvim --headless -c "lua print('LunarVim 启动成功')" +q >/dev/null 2>&1; then
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "未知")
        log_success "LunarVim 启动成功 (耗时: ${duration}s)"
        return 0
    else
        log_error "LunarVim 启动失败"
        return 1
    fi
}

# 检查必要工具
check_tools() {
    log_info "检查必要工具..."
    
    local tools=(
        "lvim:LunarVim"
        "lua:Lua 解释器"
        "luac:Lua 编译器"
        "node:Node.js"
        "npm:NPM"
        "git:Git"
    )
    
    local missing=()
    
    for tool_info in "${tools[@]}"; do
        IFS=':' read -r tool desc <<< "$tool_info"
        if command -v "$tool" >/dev/null 2>&1; then
            log_success "$desc 已安装"
        else
            log_warning "$desc 未安装"
            missing+=("$desc")
        fi
    done
    
    if [[ ${#missing[@]} -gt 0 ]]; then
        log_warning "缺少工具: ${missing[*]}"
        return 1
    fi
    
    return 0
}

# 检查环境变量
check_env_vars() {
    log_info "检查环境变量..."
    
    local env_vars=(
        "OPENAI_API_KEY:OpenAI API 密钥"
        "ANTHROPIC_API_KEY:Anthropic API 密钥"
        "GEMINI_API_KEY:Gemini API 密钥"
        "GITHUB_TOKEN:GitHub Token"
    )
    
    local configured=0
    local total=${#env_vars[@]}
    
    for var_info in "${env_vars[@]}"; do
        IFS=':' read -r var_name desc <<< "$var_info"
        if [[ -n "${!var_name}" ]]; then
            log_success "$desc 已配置"
            configured=$((configured + 1))
        else
            log_warning "$desc 未配置"
        fi
    done
    
    log_info "API 密钥配置率: $configured/$total"
    
    if [[ $configured -eq 0 ]]; then
        log_warning "建议配置至少一个 API 密钥以启用 AI 功能"
    fi
}

# 生成简单报告
generate_simple_report() {
    log_info "生成测试报告..."
    
    local report_file="quick_test_report.txt"
    
    cat > "$report_file" << EOF
LunarVim 配置快速测试报告
生成时间: $(date)

测试结果:
- 配置文件语法: $1
- LunarVim 启动: $2
- 必要工具检查: $3

建议:
1. 如果启动失败，请检查错误日志
2. 配置 API 密钥以启用 AI 功能
3. 运行完整测试: ./test_config.sh

EOF
    
    log_success "测试报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始快速测试 LunarVim 配置..."
    
    cd ~/.config/lvim
    
    # 运行测试
    local syntax_result="通过"
    local startup_result="通过"
    local tools_result="通过"
    
    if ! test_config_syntax; then
        syntax_result="失败"
    fi
    
    if ! test_lunarvim_startup; then
        startup_result="失败"
    fi
    
    if ! check_tools; then
        tools_result="部分失败"
    fi
    
    check_env_vars
    generate_simple_report "$syntax_result" "$startup_result" "$tools_result"
    
    log_success "快速测试完成！"
    
    echo
    log_info "下一步建议:"
    echo "1. 如果测试失败，请查看错误信息"
    echo "2. 配置 API 密钥: cp ~/.ai_keys_template.sh ~/.ai_keys.sh"
    echo "3. 运行完整测试: ./test_config.sh"
    echo "4. 启动 LunarVim: lvim"
}

# 运行主函数
main "$@"
