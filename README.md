# LunarVim AI 集成优化配置

这是一个专为 Arch Linux + Wayfire 环境优化的 LunarVim 配置，集成了最新的 AI 编程助手和 MCP 协议支持。

## 🚀 特性

### AI 集成
- ✅ **GitHub Copilot** - 智能代码补全和聊天
- ✅ **Avante.nvim** - Cursor 风格的 AI 编辑器
- ✅ **CodeCompanion** - 多 AI 提供商支持 (OpenAI/Anthropic/Gemini)
- ✅ **MCP 协议** - Model Context Protocol 支持
- ✅ **Augment AI** - 专业 AI 编程助手

### 语言支持
- 🔧 **Golang** - 完整的 Go 开发环境
- 🦀 **Rust** - 现代 Rust 开发工具链
- 🐍 **Python** - 全功能 Python 开发支持
- ⚡ **C/C++** - 传统系统编程语言支持
- 📝 **Markdown** - 文档编写优化
- 🌐 **前端** - JavaScript/TypeScript/Vue/React 支持

### 性能优化
- ⚡ 启动时间优化 (目标 < 3 秒)
- 🧠 内存使用优化
- 📁 大文件处理优化
- 🔄 懒加载插件配置

## 📦 快速安装

### 1. 自动安装工具

```bash
# 进入 LunarVim 配置目录
cd ~/.config/lvim

# 运行自动安装脚本
./install_ai_tools.sh
```

### 2. 配置 API 密钥

```bash
# 复制模板文件
cp ~/.ai_keys_template.sh ~/.ai_keys.sh

# 编辑配置文件，填入真实的 API 密钥
nvim ~/.ai_keys.sh

# 在 shell 配置中添加
echo "source ~/.ai_keys.sh" >> ~/.zshrc
source ~/.zshrc
```

### 3. 测试配置

```bash
# 运行配置测试
./test_config.sh

# 启动 LunarVim
lvim

# 在 LunarVim 中检查状态
:AIKeysStatus
:LvimInfo
:Mason
```

## 🎯 使用指南

### AI 功能快捷键

| 快捷键 | 功能 | 描述 |
|--------|------|------|
| `,Ac` | Copilot 聊天 | 打开 GitHub Copilot 聊天界面 |
| `,Av` | Avante 编辑器 | 启动 Cursor 风格的 AI 编辑器 |
| `,Ao` | CodeCompanion | 多 AI 提供商聊天 |
| `,Aa` | Augment 聊天 | Augment AI 助手 |
| `,Amh` | MCP Hub | 打开 MCP 协议管理界面 |

### 代码生成快捷键

| 快捷键 | 功能 | 描述 |
|--------|------|------|
| `,Agf` | 修复代码 | AI 自动修复代码问题 |
| `,Ago` | 优化代码 | AI 代码性能优化建议 |
| `,Agt` | 生成测试 | 自动生成单元测试 |
| `,Agd` | 生成文档 | 自动生成代码文档 |
| `,Agr` | 重构代码 | AI 辅助代码重构 |

### 语言特定快捷键

#### Go 开发
| 快捷键 | 功能 |
|--------|------|
| `,lgr` | 运行 Go 程序 |
| `,lgt` | 运行测试 |
| `,lgf` | 格式化代码 |
| `,lgl` | Lint 检查 |

#### Rust 开发
| 快捷键 | 功能 |
|--------|------|
| `,lrr` | 运行 Rust 项目 |
| `,lrt` | 运行测试 |
| `,lrf` | 格式化代码 |
| `,lrl` | Clippy 检查 |

#### Python 开发
| 快捷键 | 功能 |
|--------|------|
| `,lpr` | 运行 Python 文件 |
| `,lpt` | 运行测试 |
| `,lpf` | Black 格式化 |
| `,lpl` | Flake8 检查 |

## 🔧 配置结构

```
~/.config/lvim/
├── config.lua              # 主配置文件
├── lua/user/
│   ├── options.lua         # 编辑器选项
│   ├── plugins.lua         # 插件配置
│   ├── keymaps.lua         # 快捷键映射
│   ├── languages/          # 语言特定配置
│   │   ├── init.lua
│   │   ├── golang.lua
│   │   ├── rust.lua
│   │   ├── python.lua
│   │   └── c_cpp.lua
│   └── ai/                 # AI 集成配置
│       ├── init.lua
│       ├── secrets.lua
│       └── mcp.lua
├── install_ai_tools.sh     # 自动安装脚本
├── test_config.sh          # 配置测试脚本
└── README.md               # 使用说明
```

## 🛠️ 故障排除

### 常见问题

#### 1. LunarVim 启动慢
```bash
# 检查插件状态
:Lazy profile

# 禁用不需要的插件
# 编辑 lua/user/plugins.lua
```

#### 2. AI 功能不工作
```bash
# 检查 API 密钥
:AIKeysStatus

# 测试连接
:AIKeysTest

# 检查插件状态
:Copilot status
```

#### 3. LSP 服务器未启动
```bash
# 检查 LSP 状态
:LspInfo

# 安装缺失的服务器
:Mason

# 重启 LSP
:LspRestart
```

#### 4. MCP 服务器连接失败
```bash
# 检查 MCP 服务器状态
:lua require('user.ai.mcp').show_server_status()

# 重新安装 MCP 服务器
:lua require('user.ai.mcp').install_servers()
```

### 性能优化

#### 启动时间优化
1. 使用懒加载插件配置
2. 减少自动命令数量
3. 优化插件加载顺序

#### 内存使用优化
1. 禁用不需要的功能
2. 配置大文件处理
3. 定期清理缓存

## 📚 进阶配置

### 自定义 AI 提示

在 `lua/user/ai/workflows.lua` 中添加自定义提示：

```lua
local custom_prompts = {
  chinese_explain = {
    prompt = "请用中文详细解释这段代码的功能和原理",
    mapping = "<leader>Ce",
  },
  optimize_performance = {
    prompt = "请分析这段代码的性能瓶颈并提供优化建议",
    mapping = "<leader>Co",
  },
}
```

### 添加新的语言支持

1. 在 `lua/user/languages/` 目录下创建新的语言配置文件
2. 在 `lua/user/languages/init.lua` 中添加语言名称
3. 配置 LSP、格式化工具和调试器

### 集成本地 AI 模型

```lua
-- 在 lua/user/ai/init.lua 中添加
local ollama_config = {
  provider = "ollama",
  model = "codellama:7b",
  endpoint = "http://localhost:11434",
}
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

### 开发环境设置

```bash
# 克隆配置
git clone <your-repo> ~/.config/lvim

# 安装开发工具
./install_ai_tools.sh

# 运行测试
./test_config.sh
```

## 📄 许可证

MIT License

## 🙏 致谢

- [LunarVim](https://lunarvim.org/) - 优秀的 Neovim 配置框架
- [GitHub Copilot](https://github.com/features/copilot) - AI 编程助手
- [Avante.nvim](https://github.com/yetone/avante.nvim) - Cursor 风格编辑器
- [MCP Hub](https://github.com/ravitemer/mcphub.nvim) - MCP 协议支持
- Arch Linux 社区 - 优秀的 Linux 发行版

---

**享受 AI 增强的编程体验！** 🚀
