#!/bin/bash
# ~/.config/lvim/check_ai_keys.sh
# 检查AI密钥配置状态

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== AI 密钥配置状态检查 ===${NC}"
echo

# 检查函数
check_key() {
    local key_name="$1"
    local key_value="$2"
    local is_required="$3"
    
    if [ -n "$key_value" ] && [ "$key_value" != "your-${key_name,,}-api-key-here" ] && [ "$key_value" != "ghp_your-github-token-here" ] && [ "$key_value" != "sk-your-openai-api-key-here" ] && [ "$key_value" != "sk-ant-your-anthropic-api-key-here" ]; then
        echo -e "✅ ${GREEN}$key_name${NC}: 已配置"
        return 0
    else
        if [ "$is_required" = "true" ]; then
            echo -e "❌ ${RED}$key_name${NC}: 未配置 (必需)"
        else
            echo -e "⚠️  ${YELLOW}$key_name${NC}: 未配置 (可选)"
        fi
        return 1
    fi
}

# 重新加载环境变量
source ~/.zshrc 2>/dev/null

# 检查各个密钥
echo "检查必需的API密钥:"
check_key "OPENAI_API_KEY" "$OPENAI_API_KEY" "true"
check_key "ANTHROPIC_API_KEY" "$ANTHROPIC_API_KEY" "true"
check_key "GOOGLE_API_KEY" "$GOOGLE_API_KEY" "true"
check_key "GITHUB_TOKEN" "$GITHUB_TOKEN" "true"

echo
echo "检查可选的API密钥:"
check_key "AUGMENT_API_KEY" "$AUGMENT_API_KEY" "false"

echo
echo -e "${BLUE}=== 配置指南 ===${NC}"
echo "1. 编辑 ~/.zshrc 文件:"
echo "   nvim ~/.zshrc"
echo
echo "2. 找到AI密钥配置部分，替换为真实的API密钥"
echo
echo "3. 重新加载配置:"
echo "   source ~/.zshrc"
echo
echo "4. 在LunarVim中验证:"
echo "   :lua print(os.getenv('OPENAI_API_KEY'))"
echo
echo -e "${BLUE}=== API密钥获取地址 ===${NC}"
echo "• OpenAI: https://platform.openai.com/api-keys"
echo "• Anthropic: https://console.anthropic.com/"
echo "• GitHub: https://github.com/settings/tokens"
echo "• Augment: https://augmentcode.com/"
