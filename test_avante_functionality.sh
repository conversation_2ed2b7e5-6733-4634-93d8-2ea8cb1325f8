#!/bin/bash
# 测试 Avante 功能的脚本

echo "🎯 测试 Avante AI 助手功能"
echo "================================"

# 检查 Avante 插件是否加载
echo "1. 检查 Avante 插件加载状态..."
lvim --headless -c "lua if pcall(require, 'avante') then print('✅ Avante 插件已加载') else print('❌ Avante 插件未加载') end" -c "qa"

echo ""
echo "2. 检查 Avante 命令是否可用..."
lvim --headless -c "command AvanteAsk" -c "echo '✅ AvanteAsk 命令可用'" -c "qa" 2>/dev/null || echo "❌ AvanteAsk 命令不可用"

echo ""
echo "3. 检查依赖插件..."
echo "   - 检查 plenary.nvim..."
lvim --headless -c "lua if pcall(require, 'plenary') then print('✅ plenary.nvim 已加载') else print('❌ plenary.nvim 未加载') end" -c "qa"

echo "   - 检查 nui.nvim..."
lvim --headless -c "lua if pcall(require, 'nui') then print('✅ nui.nvim 已加载') else print('❌ nui.nvim 未加载') end" -c "qa"

echo "   - 检查 render-markdown.nvim..."
lvim --headless -c "lua if pcall(require, 'render-markdown') then print('✅ render-markdown.nvim 已加载') else print('❌ render-markdown.nvim 未加载') end" -c "qa"

echo ""
echo "4. 检查 Avante 配置..."
lvim --headless -c "lua local avante = require('avante'); if avante.config then print('✅ Avante 配置已加载') print('   提供商: ' .. (avante.config.provider or 'unknown')) else print('❌ Avante 配置未加载') end" -c "qa"

echo ""
echo "5. 测试快捷键配置..."
echo "   主要快捷键:"
echo "   - ,Vt : 切换 Avante 侧边栏"
echo "   - ,Va : 询问 AI"
echo "   - ,Vc : 开始聊天"
echo "   - ,Ve : 编辑选中代码"
echo ""
echo "   或者使用统一 AI 菜单:"
echo "   - ,Av : Avante 功能菜单"

echo ""
echo "6. API 密钥检查..."
if [ -n "$ANTHROPIC_API_KEY" ] || [ -n "$AVANTE_ANTHROPIC_API_KEY" ]; then
    echo "✅ Claude API 密钥已设置"
elif [ -n "$OPENAI_API_KEY" ] || [ -n "$AVANTE_OPENAI_API_KEY" ]; then
    echo "✅ OpenAI API 密钥已设置"
elif [ -n "$GOOGLE_API_KEY" ] || [ -n "$AVANTE_GEMINI_API_KEY" ]; then
    echo "✅ Gemini API 密钥已设置"
else
    echo "⚠️ 未检测到 AI API 密钥"
    echo "   请设置以下环境变量之一:"
    echo "   - AVANTE_ANTHROPIC_API_KEY (推荐)"
    echo "   - AVANTE_OPENAI_API_KEY"
    echo "   - AVANTE_GEMINI_API_KEY"
fi

echo ""
echo "7. 使用建议..."
echo "   1. 打开一个代码文件"
echo "   2. 按 ,Vt 打开 Avante 侧边栏"
echo "   3. 按 ,Va 询问 AI 关于代码的问题"
echo "   4. 选中代码后按 ,Ve 让 AI 编辑代码"

echo ""
echo "🎉 Avante 功能测试完成！"
