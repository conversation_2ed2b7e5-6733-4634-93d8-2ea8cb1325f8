#!/bin/bash
# ~/.config/lvim/install_mcp.sh
# MCP (Model Context Protocol) 服务器安装和配置脚本
# 专为 Arch Linux + Wayfire 环境优化

set -euo pipefail

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查系统依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    local missing_deps=()
    
    if ! command_exists node; then
        missing_deps+=("nodejs")
    fi
    
    if ! command_exists npm; then
        missing_deps+=("npm")
    fi
    
    if ! command_exists npx; then
        log_warning "npx 未找到，但通常随 npm 一起安装"
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "缺少依赖: ${missing_deps[*]}"
        log_info "正在安装缺少的依赖..."
        
        # 使用 pacman 安装
        for dep in "${missing_deps[@]}"; do
            log_info "安装 $dep..."
            sudo pacman -S --noconfirm "$dep"
        done
    fi
    
    # 显示版本信息
    log_success "Node.js 版本: $(node --version)"
    log_success "npm 版本: $(npm --version)"
}

# 配置 npm 环境
setup_npm_environment() {
    log_info "配置 npm 环境..."
    
    # 创建用户级全局目录
    local npm_global="$HOME/.npm-global"
    if [ ! -d "$npm_global" ]; then
        mkdir -p "$npm_global"
        log_success "创建 npm 全局目录: $npm_global"
    fi
    
    # 配置 npm prefix
    npm config set prefix "$npm_global"
    log_success "设置 npm prefix: $npm_global"
    
    # 配置中国镜像源（优化网络访问）
    local current_registry=$(npm config get registry)
    if [[ ! "$current_registry" =~ "npmmirror.com" ]]; then
        npm config set registry https://registry.npmmirror.com
        log_success "设置 npm 中国镜像源"
    fi
    
    # 更新 PATH
    local npm_bin="$npm_global/bin"
    if [[ ":$PATH:" != *":$npm_bin:"* ]]; then
        export PATH="$npm_bin:$PATH"
        log_success "更新 PATH 环境变量"
    fi
    
    # 更新 shell 配置
    local shell_config=""
    if [ -n "${ZSH_VERSION:-}" ]; then
        shell_config="$HOME/.zshrc"
    elif [ -n "${BASH_VERSION:-}" ]; then
        shell_config="$HOME/.bashrc"
    fi
    
    if [ -n "$shell_config" ] && [ -f "$shell_config" ]; then
        if ! grep -q "\.npm-global/bin" "$shell_config"; then
            echo "export PATH=~/.npm-global/bin:\$PATH" >> "$shell_config"
            log_success "更新 $shell_config"
        fi
    fi
}

# 安装 MCP 服务器
install_mcp_servers() {
    log_info "安装 MCP 服务器..."
    
    local servers=(
        "@modelcontextprotocol/server-filesystem"
        "@modelcontextprotocol/server-github"
        "@modelcontextprotocol/server-sequential-thinking"
    )
    
    local failed_servers=()
    
    for server in "${servers[@]}"; do
        log_info "安装 $server..."
        
        if npm install -g "$server"; then
            log_success "✅ $server 安装成功"
        else
            log_error "❌ $server 安装失败"
            failed_servers+=("$server")
        fi
    done
    
    if [ ${#failed_servers[@]} -gt 0 ]; then
        log_warning "以下服务器安装失败: ${failed_servers[*]}"
        return 1
    fi
    
    log_success "所有 MCP 服务器安装完成！"
}

# 验证安装
verify_installation() {
    log_info "验证 MCP 服务器安装..."
    
    local servers=(
        "@modelcontextprotocol/server-filesystem"
        "@modelcontextprotocol/server-github"
        "@modelcontextprotocol/server-sequential-thinking"
    )
    
    local all_ok=true
    
    for server in "${servers[@]}"; do
        if npm list -g "$server" >/dev/null 2>&1; then
            local version=$(npm list -g "$server" 2>/dev/null | grep "$server" | sed 's/.*@//' | sed 's/ .*//')
            log_success "✅ $server@$version"
        else
            log_error "❌ $server 未安装"
            all_ok=false
        fi
    done
    
    return $all_ok
}

# 测试 MCP 服务器
test_mcp_servers() {
    log_info "测试 MCP 服务器..."
    
    local test_dir="/tmp/mcp_test_$$"
    mkdir -p "$test_dir"
    cd "$test_dir"
    
    # 测试文件系统服务器
    log_info "测试文件系统服务器..."
    if timeout 5 npx @modelcontextprotocol/server-filesystem "$test_dir" --help >/dev/null 2>&1; then
        log_success "✅ 文件系统服务器可执行"
    else
        log_warning "⚠️ 文件系统服务器测试超时或失败"
    fi
    
    # 测试 GitHub 服务器
    log_info "测试 GitHub 服务器..."
    if timeout 5 npx @modelcontextprotocol/server-github --help >/dev/null 2>&1; then
        log_success "✅ GitHub 服务器可执行"
    else
        log_warning "⚠️ GitHub 服务器测试超时或失败"
    fi
    
    # 测试序列思考服务器
    log_info "测试序列思考服务器..."
    if timeout 5 npx @modelcontextprotocol/server-sequential-thinking --help >/dev/null 2>&1; then
        log_success "✅ 序列思考服务器可执行"
    else
        log_warning "⚠️ 序列思考服务器测试超时或失败"
    fi
    
    # 清理测试目录
    cd - >/dev/null
    rm -rf "$test_dir"
}

# 创建 systemd 服务（可选）
create_systemd_service() {
    log_info "创建 systemd 用户服务（可选）..."
    
    local service_dir="$HOME/.config/systemd/user"
    mkdir -p "$service_dir"
    
    cat > "$service_dir/mcp-filesystem.service" << EOF
[Unit]
Description=MCP Filesystem Server
After=network.target

[Service]
Type=simple
ExecStart=$HOME/.npm-global/bin/npx @modelcontextprotocol/server-filesystem %h
Restart=always
RestartSec=5
Environment=PATH=$HOME/.npm-global/bin:/usr/bin:/bin

[Install]
WantedBy=default.target
EOF
    
    log_success "创建 systemd 服务文件: $service_dir/mcp-filesystem.service"
    log_info "使用以下命令启用服务:"
    log_info "  systemctl --user enable mcp-filesystem.service"
    log_info "  systemctl --user start mcp-filesystem.service"
}

# 显示后续步骤
show_next_steps() {
    log_success "MCP 服务器安装完成！"
    echo
    log_info "后续步骤:"
    echo "1. 重新加载 shell 配置:"
    echo "   source ~/.zshrc  # 或 source ~/.bashrc"
    echo
    echo "2. 重启 LunarVim 以加载 MCP 配置:"
    echo "   lvim"
    echo
    echo "3. 在 LunarVim 中运行诊断:"
    echo "   :MCPDiagnostics"
    echo
    echo "4. 测试 MCP 功能:"
    echo "   :MCPCheckServers"
    echo "   :MCPTestServers"
    echo
    echo "5. 配置 GitHub Token（可选）:"
    echo "   export GITHUB_TOKEN=your_token_here"
    echo
    log_info "如果遇到问题，运行 :MCPFix 进行自动修复"
}

# 主函数
main() {
    log_info "开始安装 MCP (Model Context Protocol) 服务器..."
    echo "=" * 50
    
    check_dependencies
    setup_npm_environment
    install_mcp_servers
    
    if verify_installation; then
        test_mcp_servers
        create_systemd_service
        show_next_steps
    else
        log_error "安装验证失败，请检查错误信息"
        exit 1
    fi
    
    log_success "MCP 安装脚本执行完成！"
}

# 运行主函数
main "$@"
