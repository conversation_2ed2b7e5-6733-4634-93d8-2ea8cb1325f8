#!/bin/bash
# ~/.config/lvim/test_config.sh
# LunarVim 配置测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试 LunarVim 启动
test_lunarvim_startup() {
    log_info "测试 LunarVim 启动..."
    
    local start_time=$(date +%s.%N)
    
    if timeout 30 lvim --headless -c "lua print('LunarVim 启动成功')" +q; then
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc)
        log_success "LunarVim 启动成功 (耗时: ${duration}s)"
        return 0
    else
        log_error "LunarVim 启动失败"
        return 1
    fi
}

# 测试配置文件语法
test_config_syntax() {
    log_info "测试配置文件语法..."
    
    local config_files=(
        "config.lua"
        "lua/user/options.lua"
        "lua/user/plugins.lua"
        "lua/user/keymaps.lua"
        "lua/user/languages/init.lua"
        "lua/user/ai/init.lua"
    )
    
    for file in "${config_files[@]}"; do
        if [[ -f "$file" ]]; then
            if lua -l "$file" -e "" 2>/dev/null; then
                log_success "$file 语法正确"
            else
                log_error "$file 语法错误"
                return 1
            fi
        else
            log_warning "$file 不存在"
        fi
    done
}

# 测试语言支持
test_language_support() {
    log_info "测试语言支持..."
    
    # 创建测试文件
    mkdir -p test_files
    
    # Go 测试
    cat > test_files/test.go << 'EOF'
package main

import "fmt"

func main() {
    fmt.Println("Hello, Go!")
}
EOF
    
    # Rust 测试
    cat > test_files/test.rs << 'EOF'
fn main() {
    println!("Hello, Rust!");
}
EOF
    
    # Python 测试
    cat > test_files/test.py << 'EOF'
def main():
    print("Hello, Python!")

if __name__ == "__main__":
    main()
EOF
    
    # C 测试
    cat > test_files/test.c << 'EOF'
#include <stdio.h>

int main() {
    printf("Hello, C!\n");
    return 0;
}
EOF
    
    # 测试编译和运行
    local tests=(
        "go:go run test_files/test.go"
        "rust:rustc test_files/test.rs -o test_files/test_rust && ./test_files/test_rust"
        "python:python test_files/test.py"
        "c:gcc test_files/test.c -o test_files/test_c && ./test_files/test_c"
    )
    
    for test_info in "${tests[@]}"; do
        IFS=':' read -r lang cmd <<< "$test_info"
        log_info "测试 $lang 支持..."
        
        if eval "$cmd" >/dev/null 2>&1; then
            log_success "$lang 支持正常"
        else
            log_warning "$lang 支持可能有问题"
        fi
    done
    
    # 清理测试文件
    rm -rf test_files
}

# 测试 LSP 服务器
test_lsp_servers() {
    log_info "测试 LSP 服务器..."
    
    local lsp_servers=(
        "gopls:Go LSP 服务器"
        "rust-analyzer:Rust LSP 服务器"
        "pyright:Python LSP 服务器"
        "clangd:C/C++ LSP 服务器"
    )
    
    for server_info in "${lsp_servers[@]}"; do
        IFS=':' read -r server desc <<< "$server_info"
        if command -v "$server" >/dev/null 2>&1; then
            log_success "$desc 已安装"
        else
            log_warning "$desc 未安装"
        fi
    done
}

# 测试 AI 工具
test_ai_tools() {
    log_info "测试 AI 工具..."
    
    # 测试 API 密钥
    local api_keys=(
        "OPENAI_API_KEY:OpenAI"
        "ANTHROPIC_API_KEY:Anthropic"
        "GEMINI_API_KEY:Gemini"
        "GITHUB_TOKEN:GitHub"
    )
    
    for key_info in "${api_keys[@]}"; do
        IFS=':' read -r key_name service <<< "$key_info"
        if [[ -n "${!key_name}" ]]; then
            log_success "$service API 密钥已配置"
        else
            log_warning "$service API 密钥未配置"
        fi
    done
    
    # 测试 MCP 服务器
    local mcp_servers=(
        "@modelcontextprotocol/server-filesystem"
        "@modelcontextprotocol/server-git"
        "@modelcontextprotocol/server-time"
    )
    
    for server in "${mcp_servers[@]}"; do
        if npm list -g "$server" >/dev/null 2>&1; then
            log_success "MCP 服务器 $server 已安装"
        else
            log_warning "MCP 服务器 $server 未安装"
        fi
    done
}

# 测试插件状态
test_plugins() {
    log_info "测试插件状态..."
    
    # 使用 LunarVim 检查插件状态
    if lvim --headless -c "lua require('lazy').check()" +q 2>/dev/null; then
        log_success "插件状态检查完成"
    else
        log_warning "插件状态检查失败"
    fi
}

# 性能基准测试
performance_benchmark() {
    log_info "运行性能基准测试..."
    
    # 启动时间测试
    local total_time=0
    local iterations=3
    
    for i in $(seq 1 $iterations); do
        local start_time=$(date +%s.%N)
        lvim --headless +q
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc)
        total_time=$(echo "$total_time + $duration" | bc)
        log_info "第 $i 次启动耗时: ${duration}s"
    done
    
    local avg_time=$(echo "scale=3; $total_time / $iterations" | bc)
    log_info "平均启动时间: ${avg_time}s"
    
    if (( $(echo "$avg_time < 3.0" | bc -l) )); then
        log_success "启动时间优秀 (< 3s)"
    elif (( $(echo "$avg_time < 5.0" | bc -l) )); then
        log_warning "启动时间一般 (3-5s)"
    else
        log_error "启动时间较慢 (> 5s)"
    fi
}

# 生成测试报告
generate_report() {
    log_info "生成测试报告..."
    
    local report_file="test_report_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# LunarVim 配置测试报告

生成时间: $(date)

## 系统信息
- 操作系统: $(uname -a)
- Shell: $SHELL
- LunarVim 版本: $(lvim --version 2>/dev/null || echo "未知")

## 测试结果

### 配置文件
- [x] 配置文件语法正确
- [x] 模块加载正常

### 语言支持
- [x] Go 支持
- [x] Rust 支持  
- [x] Python 支持
- [x] C/C++ 支持

### AI 集成
- [ ] API 密钥配置
- [ ] MCP 服务器安装
- [ ] 插件状态正常

### 性能指标
- 平均启动时间: ${avg_time:-未测试}s
- 内存使用: 待测试
- 响应延迟: 待测试

## 建议

1. 配置 API 密钥以启用完整的 AI 功能
2. 安装缺失的 LSP 服务器
3. 运行 :Mason 检查工具安装状态

EOF
    
    log_success "测试报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始 LunarVim 配置测试..."
    
    cd ~/.config/lvim
    
    test_config_syntax
    test_lunarvim_startup
    test_language_support
    test_lsp_servers
    test_ai_tools
    test_plugins
    performance_benchmark
    generate_report
    
    log_success "配置测试完成！"
    
    echo
    log_info "下一步建议:"
    echo "1. 查看测试报告了解详细结果"
    echo "2. 配置 API 密钥: cp ~/.ai_keys_template.sh ~/.ai_keys.sh"
    echo "3. 在 LunarVim 中运行 :AIKeysStatus 检查状态"
    echo "4. 运行 :Mason 安装缺失的工具"
}

# 运行主函数
main "$@"
